{"type": "module", "private": true, "packageManager": "pnpm@10.8.0", "scripts": {"build": "nuxi build", "dev:pwa": "VITE_PLUGIN_PWA=true nuxi dev", "dev": "nuxi dev", "preview": "nuxt preview", "generate": "nuxi generate", "prepare": "nuxi prepare", "start": "node .output/server/index.mjs", "start:generate": "npx serve .output/public", "lint": "eslint .", "typecheck": "nuxi typecheck"}, "dependencies": {"@element-plus/icons-vue": "catalog:", "@nuxtjs/i18n": "catalog:", "@popperjs/core": "^2.11.8", "echarts": "^5.6.0", "element-plus": "catalog:", "mitt": "catalog:", "vuedraggable": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "catalog:dev", "@element-plus/nuxt": "catalog:", "@nuxt/devtools": "catalog:build", "@nuxt/eslint": "catalog:build", "@nuxt/icon": "1.13.0", "@nuxtjs/google-fonts": "catalog:", "@pinia/nuxt": "catalog:build", "@unocss/eslint-config": "catalog:build", "@unocss/nuxt": "catalog:build", "@vueuse/core": "catalog:frontend", "@vueuse/nuxt": "catalog:build", "eslint": "catalog:dev", "eslint-plugin-format": "catalog:dev", "nuxt": "catalog:build", "pinia": "catalog:frontend", "sass": "catalog:", "typescript": "catalog:dev", "unocss": "catalog:build", "vue": "catalog:frontend", "vue-tsc": "catalog:dev"}, "resolutions": {"@nuxt/devtools": "catalog:build", "unplugin": "catalog:build", "vite": "catalog:build"}}