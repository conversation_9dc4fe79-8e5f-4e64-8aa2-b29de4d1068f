/**
 * Admin API代理处理程序
 * 用于将前端请求转发到后端API服务
 * 使用方法: /admin-api/some-path 会被代理到 config.adminApiBaseUrl/admin-api/some-path
 */
import { defineEventHandler, getQuery, getRequestHeaders, readBody } from 'h3'

export default defineEventHandler(async (event) => {
  try {
    // 获取运行时配置
    const config = useRuntimeConfig()

    // 获取请求路径，移除 /admin-api 前缀，获取实际的API路径
    const path = event.path.replace(/^\/admin-api\//, '')

    // 构建目标URL，保持 admin-api 前缀
    const targetUrl = `${config.adminApiBaseUrl}/admin-api/${path}`

    // 获取请求方法
    const method = event.method

    // 获取原始请求头
    const originalHeaders = getRequestHeaders(event)
    const headersToForward: Record<string, string> = {}

    // 转发大部分头部，过滤掉一些不应直接代理的头部
    for (const key in originalHeaders) {
      if (!['host', 'connection', 'content-length'].includes(key.toLowerCase())) {
        headersToForward[key] = originalHeaders[key]!
      }
    }

    // 检查授权令牌，从cookie中获取并设置到Authorization头
    const accessToken = getCookie(event, 'accessToken') || ''
    if (accessToken) {
      headersToForward.authorization = `Bearer ${accessToken}`
    }

    // 获取查询参数
    const query = getQuery(event)

    // 构建请求选项
    const fetchOptions: any = {
      method,
      headers: headersToForward,
      // 添加超时设置，30秒
      timeout: 30000,
      // 重试选项
      retry: 0,
    }

    // 对于POST/PUT/PATCH请求，读取并转发请求体
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      fetchOptions.body = await readBody(event)
      // 当 body 是 FormData 时 (通常由 readBody 对 multipart/form-data 解析得到)，
      // fetch API 会自动设置 Content-Type 并包含正确的 boundary。
      // 因此，从 headersToForward 中删除 Content-Type，以避免冲突。
      if (fetchOptions.body instanceof FormData) {
        delete headersToForward['content-type']
      }
    }

    // 发送请求到目标服务器
    const response = await $fetch(targetUrl, {
      ...fetchOptions,
      query,
    })

    // 返回响应结果
    return response
  }
  catch (error: any) {
    // 记录错误信息
    console.error('Admin API Proxy 错误:', error.message)

    // 返回适当的错误响应
    return {
      code: error.statusCode || 500,
      data: null,
      msg: `代理请求失败: ${error.message || '未知错误，请稍后重试'}`,
    }
  }
})
