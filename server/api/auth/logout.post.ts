import type { H3Event } from 'h3'
import { defineEventHandler, deleteCookie, getCookie } from 'h3'

export default defineEventHandler(async (event: H3Event) => {
  // 1. 从 Cookie 中获取 token
  const token = getCookie(event, 'token') // 假设 token 存储在名为 'token' 的 cookie 中

  // 定义后端登出 URL
  // TODO: 建议将基础 URL 配置在环境变量或运行时配置中

  try {
    const config = useRuntimeConfig()

    // 2. 如果存在 token，调用后端登出接口
    if (token) {
      try {
        // 使用 Nuxt 3 内置的 $fetch 或 ofetch
        await $fetch(`${config.adminApiBaseUrl}/admin-api/system/auth/logout`, {
          method: 'POST',
          headers: {
            // 根据后端要求传递认证信息，通常是 Bearer Token
            Authorization: `Bearer ${token}`,
          },
          // 即使后端返回错误（例如token已失效），也继续执行后续步骤（清除cookie）
        })
      }
      catch (fetchError) {
        // 记录调用后端接口时的错误，但不中断流程，因为重点是清除本地 token
        console.error('Backend logout request failed:', fetchError)
        // 这里可以选择是否向上抛出错误，取决于业务逻辑：
        // 如果希望前端知道后端登出失败，则应抛出错误。
        // 如果主要目的是保证前端登出，则可以仅记录日志。
      }
    }

    // 3. 清除客户端的 token cookie
    // 确保使用与设置 cookie 时相同的选项（path, httpOnly, secure, sameSite）
    deleteCookie(event, 'accessToken', {
      httpOnly: true,
      path: '/',
      // 在生产环境中应添加 secure: true 和 sameSite: 'lax' 或 'strict'
      // secure: process.env.NODE_ENV === 'production',
      // sameSite: 'lax'
    })
    deleteCookie(event, 'refreshToken', {
      httpOnly: true,
      path: '/',

    })

    // 4. 返回成功响应
    return { success: true, message: 'Logout successful.' }
  }
  catch (error) {
    // 捕获清除 cookie 或其他意外错误
    console.error('Error during logout process:', error)

    // 向上抛出错误，让 Nuxt 的错误处理机制接管
    throw createError({
      statusCode: 500,
      statusMessage: 'Logout failed due to an internal error.',
    })
  }
})
