import { defineEventHand<PERSON>, readBody, setCookie } from 'h3'

interface LoginRequest {
  tenantName: string
  username: string
  password: string
  rememberMe: boolean
}

interface LoginResponse {
  code: number
  data: {
    userId: number
    accessToken: string
    refreshToken: string
    expiresTime: number
  }
  msg: string
}

/**
 * 登录API端点
 * 代理请求到管理系统后端
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取运行时配置
    const config = useRuntimeConfig()

    // 读取请求体
    const body = await readBody<LoginRequest>(event)
    // 调用管理系统的登录API
    const response = await $fetch<LoginResponse>(`${config.adminApiBaseUrl}/admin-api/system/auth/login2`, {
      method: 'POST',
      body
    })

    // 如果登录成功，可以在这里设置Cookie
    if (response.code === 0 && response.data.accessToken) {
      setCookie(event, 'accessToken', response.data.accessToken, {
        httpOnly: true,
        path: '/',
        maxAge: 60 * 60 * 0.5, // 30分钟
        sameSite: 'strict',
      })

      setCookie(event, 'refreshToken', response.data.refreshToken, {
        httpOnly: true,
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7天
        sameSite: 'strict',
      })
    }

    // 返回登录结果
    return response
  }
  catch (error) {
    // 处理错误
    console.error('登录失败:', error)
    return {
      code: 500,
      data: null,
      msg: '登录请求失败，请稍后重试',
    }
  }
})
