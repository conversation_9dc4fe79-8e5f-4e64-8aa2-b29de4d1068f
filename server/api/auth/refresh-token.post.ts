import { defineEvent<PERSON><PERSON><PERSON>, getCookie, setCookie } from 'h3'

interface RefreshTokenResponse {
  code: number
  data: {
    userId: number
    accessToken: string
    refreshToken: string
    expiresTime: number
  }
  msg: string
}

/**
 * Token刷新API端点
 * 使用refreshToken获取新的accessToken
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取运行时配置
    const config = useRuntimeConfig()

    // 从 Cookie 中获取refreshToken
    const refreshToken = getCookie(event, 'refreshToken')

    if (!refreshToken) {
      return {
        code: 400,
        data: null,
        msg: 'refreshToken不能为空',
      }
    }

    // 调用管理系统的刷新Token API
    const response = await $fetch<RefreshTokenResponse>(`${config.adminApiBaseUrl}/admin-api/system/auth/refresh-token?refreshToken=${refreshToken}`, {
      method: 'POST',
    })
    // 如果刷新成功，更新Cookie
    if (response.code === 0 && response.data.accessToken) {
      setCookie(event, 'accessToken', response.data.accessToken, {
        httpOnly: true,
        path: '/',
        maxAge: 60 * 60 * 0.5, // 30分钟
        sameSite: 'strict',
      })

      setCookie(event, 'refreshToken', response.data.refreshToken, {
        httpOnly: true,
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7天
        sameSite: 'strict',
      })
    }

    // 返回刷新结果
    return response
  }
  catch (error) {
    // 处理错误
    console.error('Token刷新失败:', error)
    return {
      code: 500,
      data: null,
      msg: 'Token刷新失败，请重新登录',
    }
  }
})
