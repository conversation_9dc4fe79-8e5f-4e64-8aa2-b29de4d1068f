import { defineEvent<PERSON><PERSON><PERSON>, getCookie } from 'h3'

interface PermissionInfoResponse {
  code: number
  data: {
    user: {
      id: number
      nickname: string
      avatar: string
      deptId: number
    }
    roles: string[]
    permissions: string[]
  }
  msg: string
}

/**
 * 获取用户权限信息的API端点
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取运行时配置
    const config = useRuntimeConfig()

    // 从Cookie中获取访问令牌
    const accessToken = getCookie(event, 'accessToken')
    // 如果没有令牌，返回未授权错误
    if (!accessToken) {
      return {
        code: 401,
        data: null,
        msg: '未登录或登录已过期',
      }
    }

    // 调用管理系统的用户权限信息API
    const response = await $fetch<PermissionInfoResponse>(`${config.adminApiBaseUrl}/admin-api/system/auth/get-permission-info`, {
      method: 'GET',
      headers: {
        // 在请求头中传递访问令牌
        Authorization: `Bearer ${accessToken}`,
      },
    })

    // 返回用户权限信息
    return response
  }
  catch (error) {
    // 处理错误
    console.error('获取用户权限信息失败:', error)
    return {
      code: 500,
      data: null,
      msg: '获取用户权限信息失败，请稍后重试',
    }
  }
})
