/**
 * 受保护的API示例
 * 展示如何处理需要授权的API端点
 */
import { defineEventHandler, createError } from 'h3'

export default defineEventHandler(async (event) => {
  // 检查是否已认证
  if (!event.context.auth?.isAuthenticated) {
    throw createError({
      statusCode: 401,
      statusMessage: '未授权',
      message: '请先登录再访问此API',
    })
  }

  // 模拟获取用户数据
  try {
    // 这里可以调用后端API或数据库
    return {
      code: 0,
      data: {
        message: '这是受保护的API数据',
        requestedAt: new Date().toISOString(),
        userId: '示例用户ID', // 通常从令牌中解析
      },
      msg: '请求成功',
    }
  }
  catch (error: any) {
    console.error('API调用失败:', error)
    return {
      code: 500,
      data: null,
      msg: error.message || '服务器内部错误',
    }
  }
}) 