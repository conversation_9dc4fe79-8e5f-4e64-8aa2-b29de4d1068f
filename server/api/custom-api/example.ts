/**
 * 自定义API示例
 * 展示如何实现不依赖代理的自定义API功能
 */
import { defineEventHandler, getQuery, readBody } from 'h3'

// 模拟数据存储
const items = [
  { id: 1, name: '项目1', status: 'active' },
  { id: 2, name: '项目2', status: 'inactive' },
  { id: 3, name: '项目3', status: 'active' },
]

export default defineEventHandler(async (event) => {
  try {
    // 根据请求方法处理不同操作
    switch (event.method) {
      case 'GET': {
        // 获取查询参数
        const query = getQuery(event)
        const status = query.status as string | undefined
        
        // 根据状态过滤
        const filteredItems = status 
          ? items.filter(item => item.status === status)
          : items
          
        return {
          code: 0,
          data: filteredItems,
          msg: '获取成功',
        }
      }
      
      case 'POST': {
        // 创建新项目
        const body = await readBody(event)
        
        // 验证请求数据
        if (!body.name) {
          return {
            code: 400,
            data: null,
            msg: '项目名称不能为空',
          }
        }
        
        // 创建新项目（模拟）
        const newItem = {
          id: items.length + 1,
          name: body.name,
          status: body.status || 'inactive',
        }
        
        // 在实际应用中，这里会将数据保存到数据库
        items.push(newItem)
        
        return {
          code: 0,
          data: newItem,
          msg: '创建成功',
        }
      }
      
      default:
        return {
          code: 405,
          data: null,
          msg: '不支持的请求方法',
        }
    }
  }
  catch (error: any) {
    console.error('API处理错误:', error)
    return {
      code: 500,
      data: null,
      msg: error.message || '服务器内部错误',
    }
  }
})