/**
 * API辅助工具
 * 提供标准化API响应和错误处理函数
 */

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  code: number
  data: T | null
  msg: string
}

/**
 * 创建成功响应
 * @param data 响应数据
 * @param msg 成功消息
 * @returns 标准化的成功响应
 */
export function createSuccessResponse<T>(data: T, msg = '操作成功'): ApiResponse<T> {
  return {
    code: 0, // 0表示成功
    data,
    msg,
  }
}

/**
 * 创建错误响应
 * @param msg 错误消息
 * @param code 错误代码，默认为500
 * @returns 标准化的错误响应
 */
export function createErrorResponse(msg: string, code = 500): ApiResponse<null> {
  return {
    code,
    data: null,
    msg,
  }
}

/**
 * 处理API异常，转换为标准响应
 * @param error 捕获的错误
 * @returns 标准化的错误响应
 */
export function handleApiError(error: any): ApiResponse<null> {
  console.error('API错误:', error)
  
  // 根据错误类型返回不同的错误代码
  if (error.statusCode === 401) {
    return createErrorResponse('未授权，请登录后重试', 401)
  }
  
  if (error.statusCode === 403) {
    return createErrorResponse('没有权限执行此操作', 403)
  }
  
  if (error.statusCode === 404) {
    return createErrorResponse('请求的资源不存在', 404)
  }
  
  // 默认服务器错误
  return createErrorResponse(
    error.message || '服务器内部错误，请稍后重试',
    error.statusCode || 500
  )
}

/**
 * 检查授权
 * @param event 事件对象
 * @returns 是否已授权
 */
export function isAuthenticated(event: any): boolean {
  return !!event.context.auth?.isAuthenticated
}

/**
 * 获取当前用户的授权令牌
 * @param event 事件对象
 * @returns 授权令牌，未登录则返回null
 */
export function getAccessToken(event: any): string | null {
  return event.context.auth?.accessToken || null
} 