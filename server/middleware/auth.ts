/**
 * 身份验证中间件
 * 用于管理用户授权状态和请求预处理
 */
import { defineEventHandler, getCookie } from 'h3'

export default defineEventHandler((event) => {
  // 解析cookie中的授权令牌
  const accessToken = getCookie(event, 'accessToken') || ''
  const refreshToken = getCookie(event, 'refreshToken') || ''
  // 将令牌信息存储在请求上下文中
  event.context.auth = {
    isAuthenticated: !!accessToken,
    accessToken,
    refreshToken,
  }

  // 注意：这里不阻止请求，仅设置上下文信息
  // 实际的授权验证应该在各个API处理程序中完成
})
