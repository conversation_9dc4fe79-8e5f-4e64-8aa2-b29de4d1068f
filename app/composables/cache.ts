import type { CategoryApi, CountryRegionApi, SystemApi } from './api/types'
import { ElMessage } from 'element-plus'
import { acceptHMRUpdate, defineStore } from 'pinia'
import { useCategoryCache } from './api/modules/category'
import { useCountryRegionList } from './api/modules/countryRegion'
import { useDictDataList, useDictTypeListAllSimple } from './api/modules/system'

// 扩展品类类型，添加children属性用于树形结构
interface CategoryWithChildren extends CategoryApi.Category {
  children?: CategoryWithChildren[]
}

// 扩展国家地区类型，添加children属性用于树形结构
interface CountryRegionWithChildren extends CountryRegionApi.CountryRegion {
  children?: CountryRegionWithChildren[]
}

export const useCacheStore = defineStore('cache', () => {
  // 字典数据，使用Map结构按字典类型存储
  const dictCache = ref<Map<string, SystemApi.DictData[]>>(new Map())

  // 字典类型列表
  const dictTypeCache = ref<SystemApi.DictType[]>([])

  // 品类数据
  const categoryCache = ref<CategoryApi.Category[]>([])

  // 国家地区数据
  const countryRegionCache = ref<CountryRegionApi.CountryRegion[]>([])

  const countryListCache = computed(() => {
    return countryRegionCache.value.filter(item => item.type === 'country')
  })

  // 加载状态
  const loading = ref({
    dict: false,
    category: false,
    countryRegion: false,
  })

  // 字典数据是否已加载的标志
  const dictLoaded = ref<Set<string>>(new Set())

  // 字典类型是否已加载的标志
  const dictTypeLoaded = ref(false)

  // 获取指定类型的字典数据
  const getDictData = (dictType: string) => {
    return dictCache.value.get(dictType) || []
  }

  // 获取指定字典类型，通过值获取标签（支持单个值或数组）
  const getDictLabel = (dictType: string, value: string | string[]): string | string[] => {
    const dictList = getDictData(dictType)

    // 如果是数组，批量处理
    if (Array.isArray(value)) {
      return value.map(val => {
        const dictItem = dictList.find(item => item.value === val)
        return dictItem ? dictItem.label : ''
      }).join(',')
    }

    // 如果是单个值，按原逻辑处理
    const dictItem = dictList.find(item => item.value === value)
    return dictItem ? dictItem.label : ''
  }

  // 获取指定字典类型，通过值获取标签（支持单个值或数组）
  const getDictExtendValue = (dictType: string, value: string | string[]): string | string[] => {
    const dictList = getDictData(dictType)

    // 如果是数组，批量处理
    if (Array.isArray(value)) {
      return value.map(val => {
        const dictItem = dictList.find(item => item.value === val)
        return dictItem ? dictItem.extendedValue : ''
      }).join(',')
    }

    // 如果是单个值，按原逻辑处理
    const dictItem = dictList.find(item => item.value === value)
    return dictItem ? dictItem.extendedValue : ''
  }

  // 获取品类树形结构
  const getCategoryTree = computed<CategoryWithChildren[]>(() => {
    // 只返回一级和二级品类，忽略三级及以下分类
    const result: CategoryWithChildren[] = []
    const map = new Map<number, CategoryWithChildren>()

    // 辅助函数：计算分类的层级深度
    const getDepth = (item: any, categoryMap: Map<number, any>): number => {
      if (!item.parentId) return 1 // 一级分类
      const parent = categoryMap.get(item.parentId)
      if (!parent) return 1
      return getDepth(parent, categoryMap) + 1
    }

    // 创建原始数据的映射，用于计算深度
    const originalMap = new Map<number, any>()
    categoryCache.value.forEach((item) => {
      if (item.id) {
        originalMap.set(Number(item.id), item)
      }
    })

    // 只处理深度不超过2级的分类
    const filteredCategories = categoryCache.value.filter((item) => {
      if (!item.id) return false
      const depth = getDepth(item, originalMap)
      return depth <= 2
    })

    // 先建立映射关系（只包含1-2级分类）
    filteredCategories.forEach((item) => {
      if (item.id) {
        map.set(Number(item.id), { ...item, children: [] })
      }
    })

    // 构建树形结构
    filteredCategories.forEach((item) => {
      const currentItem = map.get(Number(item.id))
      if (currentItem) {
        if (item.parentId && map.has(item.parentId)) {
          const parent = map.get(item.parentId)
          if (parent && !parent.children) {
            parent.children = []
          }
          parent?.children?.push(currentItem)
        }
        else {
          result.push(currentItem)
        }
      }
    })

    return result
  })

  // 获取国家地区树形结构
  const getCountryRegionTree = computed<CountryRegionWithChildren[]>(() => {
    // 类似于品类树的构建逻辑
    const result: CountryRegionWithChildren[] = []
    const map = new Map<number, CountryRegionWithChildren>()

    // 先建立映射关系
    countryRegionCache.value.forEach((item) => {
      if (item.id) {
        map.set(Number(item.id), { ...item, children: [] })
      }
    })

    // 构建树形结构
    countryRegionCache.value.forEach((item) => {
      const currentItem = map.get(Number(item.id))
      if (currentItem) {
        if (item.parentId && map.has(item.parentId)) {
          const parent = map.get(item.parentId)
          if (parent && !parent.children) {
            parent.children = []
          }
          parent?.children?.push(currentItem)
        }
        else {
          result.push(currentItem)
        }
      }
    })

    return result
  })

  // 加载指定类型的字典数据
  const loadDictData = async () => {
    loading.value.dict = true
    try {
      const response = await useDictDataList()
      if (response && response.code === 0 && response.data) {
        // 将字典数据按 dictType 分组存储到 dictCache 中
        const dictDataList = response.data

        // 先创建一个临时映射来分组数据
        const dictMap = new Map<string, SystemApi.DictData[]>()

        // 遍历所有字典数据，按 dictType 分组
        dictDataList.forEach((item) => {
          const { dictType } = item
          if (!dictMap.has(dictType)) {
            dictMap.set(dictType, [])
          }
          dictMap.get(dictType)?.push(item)
        })

        // 更新 dictCache
        dictCache.value = dictMap

        // 标记所有字典类型为已加载
        dictMap.forEach((_, type) => {
          dictLoaded.value.add(type)
        })
      }
      else {
        ElMessage.error(`获取字典数据失败: ${response?.msg || '未知错误'}`)
      }
    }
    catch (error: any) {
      ElMessage.error(`获取字典数据出错: ${error.message || '未知错误'}`)
    }
    finally {
      loading.value.dict = false
    }
  }

  // 加载字典类型列表
  const loadDictTypeList = async () => {
    try {
      const response = await useDictTypeListAllSimple()
      if (response && response.code === 0) {
        dictTypeCache.value = response.data || []
        dictTypeLoaded.value = true
      }
      else {
        ElMessage.error(`获取字典类型列表失败: ${response?.msg || '未知错误'}`)
      }
    }
    catch (error: any) {
      ElMessage.error(`获取字典类型列表出错: ${error.message || '未知错误'}`)
    }
  }

  // 加载品类数据
  const loadCategoryData = async (locale = 'zh') => {
    loading.value.category = true
    try {
      const response = await useCategoryCache(undefined, 0, locale)
      if (response.code === 0) {
        categoryCache.value = response.data || []
      }
      else {
        ElMessage.error(`获取品类数据失败: ${response.msg}`)
      }
    }
    catch (error: any) {
      ElMessage.error(`获取品类数据出错: ${error.message || '未知错误'}`)
    }
    finally {
      loading.value.category = false
    }
  }

  // 加载国家地区数据
  const loadCountryRegionData = async () => {
    loading.value.countryRegion = true
    try {
      const response = await useCountryRegionList(undefined, 0) // 获取状态为启用的所有国家地区
      if (response.code === 0) {
        countryRegionCache.value = response.data || []
      }
      else {
        ElMessage.error(`获取国家地区数据失败: ${response.msg}`)
      }
    }
    catch (error: any) {
      ElMessage.error(`获取国家地区数据出错: ${error.message || '未知错误'}`)
    }
    finally {
      loading.value.countryRegion = false
    }
  }

  // 初始化所有缓存数据
  const initCacheData = async (_dictTypes: string[] = [], locale = 'zh') => {
    // 如果没有指定字典类型，则加载所有字典数据

    return Promise.all([
      loadDictData(),
      loadCategoryData(locale),
      loadCountryRegionData(),
    ])
  }

  // 判断数据是否全部已加载完成
  const isAllLoaded = computed(() => {
    // 检查所有加载状态是否完成，且数据是否已实际加载
    // const loadingCompleted = !loading.value.dict && !loading.value.category && !loading.value.countryRegion

    // 检查数据是否已实际加载（字典至少有一个类型被加载，品类和国家地区数组至少有内容）
    const dataLoaded = dictLoaded.value.size > 0
      && categoryCache.value.length > 0
      && countryRegionCache.value.length > 0

    return dataLoaded
  })
  const isLoading = computed(() => {
    return loading.value.dict || loading.value.category || loading.value.countryRegion
  })

  // 重置缓存数据
  const resetCache = () => {
    dictCache.value.clear()
    dictLoaded.value.clear()
    dictTypeCache.value = []
    dictTypeLoaded.value = false
    categoryCache.value = []
    countryRegionCache.value = []
  }

  // 返回store的状态和方法
  return {
    // 状态
    dictCache,
    dictTypeCache,
    categoryCache,
    countryRegionCache,
    countryListCache,
    loading,
    dictLoaded,
    dictTypeLoaded,
    isLoading,

    // 计算属性
    getCategoryTree,
    getCountryRegionTree,

    isAllLoaded,

    // 方法
    getDictData,
    getDictLabel,
    getDictExtendValue,
    loadDictTypeList,
    loadDictData,
    loadCategoryData,
    loadCountryRegionData,
    initCacheData,
    resetCache,
  }
})

// 支持HMR（热模块替换）
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useCacheStore, import.meta.hot))
}
