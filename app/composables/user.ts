import { acceptHMRUpdate, defineStore } from 'pinia'

interface UserInfo {
  id: number
  nickname: string
  avatar: string
  deptId: number
}

interface PermissionInfo {
  user: UserInfo
  roles: string[]
  permissions: string[]
}

export const useUserStore = defineStore('user', () => {
  /**
   * Current named of the user.
   */
  const savedName = ref('')
  const previousNames = ref(new Set<string>())

  // 用户权限信息
  const userInfo = ref<UserInfo | null>(null)
  const roles = ref<string[]>([])
  const permissions = ref<string[]>([])
  const token = ref<string>('')
  const isLoggedIn = computed(() => !!token.value)

  const usedNames = computed(() => Array.from(previousNames.value))
  const otherNames = computed(() => usedNames.value.filter(name => name !== savedName.value))

  /**
   * Changes the current name of the user and saves the one that was used
   * before.
   *
   * @param name - new name to set
   */
  function setNewName(name: string) {
    if (savedName.value)
      previousNames.value.add(savedName.value)

    savedName.value = name
  }

  // 设置用户权限信息
  function setPermissionInfo(permissionInfo: PermissionInfo) {
    userInfo.value = permissionInfo.user
    roles.value = permissionInfo.roles
    permissions.value = permissionInfo.permissions
  }

  // 设置令牌
  function setToken(accessToken: string) {
    token.value = accessToken
  }

  // 清除用户信息
  function clearUserInfo() {
    userInfo.value = null
    roles.value = []
    permissions.value = []
    token.value = ''
  }

  // 检查是否有某个权限
  function hasPermission(permission: string): boolean {
    return permissions.value.includes(permission)
  }

  // 检查是否有某个角色
  function hasRole(role: string): boolean {
    return roles.value.includes(role)
  }

  return {
    setNewName,
    otherNames,
    savedName,
    // 用户权限相关
    userInfo,
    roles,
    permissions,
    token,
    isLoggedIn,
    setPermissionInfo,
    setToken,
    clearUserInfo,
    hasPermission,
    hasRole,
  }
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useUserStore, import.meta.hot))
