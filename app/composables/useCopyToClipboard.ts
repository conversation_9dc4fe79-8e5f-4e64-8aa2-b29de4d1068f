/**
 * 兼容性复制到剪贴板工具函数
 * 支持多种复制方式，确保在各种环境下都能正常工作
 */

/**
 * 使用传统的 document.execCommand 方法复制文本
 * @param text 要复制的文本
 * @returns 是否复制成功
 */
function fallbackCopyTextToClipboard(text: string): boolean {
  const textArea = document.createElement('textarea')
  textArea.value = text

  // 避免在页面上显示
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.position = 'fixed'
  textArea.style.opacity = '0'
  textArea.style.pointerEvents = 'none'

  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    return successful
  } catch (err) {
    console.error('Fallback: 复制失败', err)
    document.body.removeChild(textArea)
    return false
  }
}

/**
 * 复制文本到剪贴板
 * 优先使用现代的 Clipboard API，失败时回退到传统方法
 * @param text 要复制的文本
 * @returns Promise<boolean> 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  if (!text) {
    console.warn('复制内容为空')
    return false
  }

  // 检查是否支持现代的 Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch (err) {
      console.warn('Clipboard API 复制失败，尝试回退方法:', err)
      // 如果 Clipboard API 失败，尝试回退方法
      return fallbackCopyTextToClipboard(text)
    }
  } else {
    // 不支持 Clipboard API 或不在安全上下文中，使用回退方法
    console.warn('不支持 Clipboard API，使用回退方法')
    return fallbackCopyTextToClipboard(text)
  }
}

/**
 * 复制到剪贴板的组合式函数
 * 提供响应式的复制状态和消息提示
 */
export function useCopyToClipboard() {
  const copying = ref(false)
  const lastCopiedText = ref('')

  /**
   * 复制文本并显示消息提示
   * @param text 要复制的文本
   * @param successMessage 成功提示消息
   * @param errorMessage 失败提示消息
   * @returns Promise<boolean> 是否复制成功
   */
  const copy = async (
    text: string,
    successMessage: string = 'Copied successfully',
    errorMessage: string = 'Failed to copy'
  ): Promise<boolean> => {
    if (copying.value) {
      return false
    }

    copying.value = true

    try {
      const success = await copyToClipboard(text)
      
      if (success) {
        lastCopiedText.value = text
        ElMessage({
          message: successMessage,
          type: 'success',
          duration: 2000,
        })
        return true
      } else {
        ElMessage({
          message: errorMessage,
          type: 'error',
          duration: 3000,
        })
        return false
      }
    } catch (error) {
      console.error('复制过程中发生错误:', error)
      ElMessage({
        message: errorMessage,
        type: 'error',
        duration: 3000,
      })
      return false
    } finally {
      copying.value = false
    }
  }

  return {
    copy,
    copying: readonly(copying),
    lastCopiedText: readonly(lastCopiedText),
  }
}
