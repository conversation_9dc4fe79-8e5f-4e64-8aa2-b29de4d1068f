# API 接口管理

本目录包含项目的 API 接口管理模块，采用模块化设计，方便管理不同业务模块的 API 调用。

## 目录结构

```
app/composables/api/
├── README.md          # 本文档
├── index.ts           # 核心 API 功能
├── types.ts           # 类型定义
└── modules/           # 业务模块
    ├── index.ts       # 模块统一导出
    ├── user.ts        # 用户模块 API
    ├── system.ts      # 系统模块 API
    ├── auth.ts        # 认证模块 API（登录注册等）
    └── ...            # 其他业务模块
```

## 使用方法

### 1. 基础 API 请求

```typescript
// 直接使用核心 API 请求函数
import { useApiRequest, useProxyRequest } from '~/composables/api'

// 发起请求
const response = await useApiRequest('/api/custom-path', {
  method: 'POST',
  body: { key: 'value' },
})

// 使用代理功能
const proxyResponse = await useProxyRequest('admin-api/system/some-path', {
  method: 'GET',
  query: { id: 1 },
})
```

### 2. 使用模块化 API

```typescript
import { useLogin, useRegisterFlow, useSendMailCode } from '~/composables/api/modules/auth'
import { useDictData } from '~/composables/api/modules/system'
// 导入特定模块的 API
import { useCurrentUser, useUserLogin } from '~/composables/api/modules/user'

// 管理后台登录
const loginResponse = await useUserLogin({
  tenantName: 'tenant1',
  username: 'admin',
  password: '123456',
  rememberMe: true,
})

// 前端邮箱登录
const emailLoginResponse = await useLogin('<EMAIL>', 'password123')

// 使用组合式 API 获取当前用户
const { user, permissions, loadUserInfo, hasPermission } = useCurrentUser()
await loadUserInfo()

// 判断是否有某权限
if (hasPermission('system:user:create')) {
  // 有权限执行操作
}

// 使用字典数据
const { dictData, getLabelByValue, getOptions } = useDictData('sys_user_sex')
// 获取选项列表用于下拉框
const options = getOptions()
// 根据值获取标签
const label = getLabelByValue('1')

// 使用注册流程
const {
  step,
  email,
  sendCode,
  validateCode,
  completeRegister,
  error
} = useRegisterFlow()

// 发送验证码
await sendCode('<EMAIL>')
// 验证验证码
await validateCode('123456')
// 完成注册
await completeRegister('password123', 'nickname')
```

### 3. TypeScript 类型支持

```typescript
import type { ApiResponse, SystemApi, UserApi } from '~/composables/api/types'

// 定义函数时使用类型
function processUser(user: UserApi.UserInfo) {
  // 处理用户信息
}

// 定义响应处理
function handleApiResponse<T>(response: ApiResponse<T>) {
  if (response.code === 0) {
    return response.data
  }
  return null
}
```

## 添加新的 API 模块

1. 在 `modules` 目录下创建新的模块文件，例如 `product.ts`
2. 在 `types.ts` 中添加相关类型定义
3. 实现模块的 API 函数
4. 在 `modules/index.ts` 中导出新模块

示例：

```typescript
import type { ApiResponse, PageParams, PageResult, ProductApi } from '../types'
// 新增类型定义 (types.ts)
// 新增模块 API (modules/product.ts)
import { useProxyRequest } from '../index'

export namespace ProductApi {
  export interface Product extends BaseEntity {
    name: string
    price: number
    status: number
  }
}

export async function useProductList(params: PageParams): Promise<ApiResponse<PageResult<ProductApi.Product>>> {
  return await useProxyRequest<PageResult<ProductApi.Product>>('admin-api/product/list', {
    method: 'GET',
    query: params,
  })
}

// 在模块索引中导出 (modules/index.ts)
export * from './product'
```

## 最佳实践

1. **命名规范**：

   - API 函数使用 `use` 前缀，如 `useUserLogin`
   - 通用操作使用动词开头，如 `createProduct`、`updateUser`
   - 组合式API以Flow结尾，如 `useRegisterFlow`

2. **错误处理**：

   - 默认情况下，API 错误会自动显示错误通知
   - 可以通过 `apiOptions` 参数控制错误处理行为

3. **类型安全**：

   - 尽量为所有 API 提供完整的类型定义
   - 使用泛型确保类型安全

4. **组合式 API**：

   - 对于复杂功能，推荐使用组合式 API 封装
   - 提供状态管理和相关操作方法

5. **模块分离**：
   - 按业务领域拆分API调用
   - 避免模块间的循环依赖
   - 不同模块之间有重复功能时，应明确职责边界
