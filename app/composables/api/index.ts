import type { $Fetch } from 'nitropack'
import type { ApiOptions, ApiResponse } from './types'
import { useNuxtApp } from '#app'
/**
 * API 核心模块
 * 提供封装的API请求函数和错误处理
 */
import { ElMessage } from 'element-plus'

/**
 * 创建通用API请求函数
 * @param url 请求URL
 * @param options 请求选项
 * @param retryCount 内部重试计数器，防止无限重试
 * @returns 封装的请求结果
 */
export async function useApiRequest<T = any>(
  url: string,
  options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    body?: any
    query?: Record<string, any>
    headers?: Record<string, string>
    apiOptions?: ApiOptions
  } = {},
  retryCount: number = 0,
): Promise<ApiResponse<T>> {
  const {
    method = 'GET',
    body,
    query,
    headers = {
      locale: useNuxtApp().$i18n?.locale?.value || 'en-US',
    },
    apiOptions = { showError: true, throwError: true },
  } = options

  try {
    // 获取 nuxtApp 实例
    const nuxtApp = useNuxtApp()
    // 使用 apiFetch 而不是原生 $fetch
    const apiFetch = nuxtApp.$apiFetch as $Fetch

    // 发起请求
    const response = await apiFetch<ApiResponse<T>>(url, {
      method,
      body,
      query,
      headers,
    })

    // 处理业务错误
    if (response.code !== 0) {
      if (apiOptions.showError) {
        ElMessage.error(apiOptions.errorMessage || response.msg || '请求失败')
      }

      // 如果设置了throwError选项，则抛出业务错误
      if (apiOptions.throwError) {
        const error = new Error(response.msg || '请求失败')
        error.response = response
        throw error
      }
    }

    return response
  }
  catch (error: any) {
    // 特殊处理 Token 刷新后需要重试的情况 (code=499)
    if (error.response?.code === 499) {
      // 防止无限重试，最多重试3次
      if (retryCount >= 3) {
        console.error('Token刷新重试次数超限，停止重试:', url)
        const errorResponse = {
          code: 401,
          data: null as any,
          msg: '登录状态异常，请重新登录',
        }
        if (apiOptions.throwError) {
          const err = new Error('登录状态异常，请重新登录')
          err.response = errorResponse
          throw err
        }
        return errorResponse
      }

      console.warn(`Token已刷新，重试请求 (${retryCount + 1}/3):`, url)
      // 递归调用自身，重新发起请求，增加重试计数
      return useApiRequest<T>(url, options, retryCount + 1)
    }

    console.error('API请求错误:', error)

    // 处理请求错误
    if (apiOptions.showError) {
      ElMessage.error(apiOptions.errorMessage || error.message || '服务器连接失败')
    }
    // 如果是我们自己抛出的错误，直接继续抛出
    if (error.response) {
      throw error
    }

    // 创建标准错误响应
    const errorResponse = {
      code: error.status || 500,
      data: null as any,
      msg: error.message || '请求失败',
    }

    // 如果设置了throwError选项，则抛出网络错误
    if (apiOptions.throwError) {
      error.response = errorResponse
      throw error
    }

    return errorResponse
  }
}

/**
 * 使用代理API请求
 * 基于服务端代理功能，转发请求到后端服务器
 */
export function useProxyRequest<T = any>(
  path: string,
  options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    body?: any
    query?: Record<string, any>
    headers?: Record<string, string>
    apiOptions?: ApiOptions
  } = {},
): Promise<ApiResponse<T>> {
  return useApiRequest<T>(`/${path}`, options)
}

/**
 * 检查API响应是否成功
 * @param response API响应对象
 * @returns 是否成功
 */
export function isApiSuccess(response: ApiResponse<any>): boolean {
  return response.code === 0
}

/**
 * 从API响应中提取数据
 * @param response API响应对象
 * @param defaultValue 默认值（如果响应失败）
 * @returns 响应数据或默认值
 */
export function getApiData<T>(response: ApiResponse<T>, defaultValue: T | null = null): T | null {
  return isApiSuccess(response) ? response.data : defaultValue
}
