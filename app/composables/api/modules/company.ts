/**
 * 公司模块 API
 * 包含公司相关接口
 */
import type { ApiResponse } from '../types'
import { ref } from 'vue'
import { useProxyRequest } from '../index'

/**
 * 公司模块类型定义
 */
// 用户比对公司记录响应
export interface CompareUserRelResp {
  id: number
  userId: number
  companyId: number[]
  createTime: string
  updateTime: string
}

// 用户收藏公司记录响应
export interface UserSaveRelResp {
  id: number
  userId: number
  companyId: number[]
  createTime: string
  updateTime: string
}

// 公司联系人信息
export interface ContactInfo {
  id: number
  language: string
  companyId: number
  name: string
  title: string
  mail: string
  phone: string
  sex: number
  createTime: string
}

// 公司基本信息
export interface CompanyInfo {
  id: number
  language: string
  name: string
  esicSource: string
  contactList: ContactInfo[]
}

// 公司业务信息
export interface BusinessInfo {
  id: number
  establishAt: string
  country: string
  province: string
  businessType: string
  annualSales: number
  annualSalesCurrency: string
  allStaff: number
  webSite: string
  legalPerson: string
  registerCapital: number
  registerCapitalCurrency: string
  vatNumber: string
  siteArea: number
  siteAreaUnit: string
  totalFloorAreaSqm: number
  totalFloorAreaSqmUnit: string
  operateStatus: string
  status: string
  createTime: string
  remark: string
  registeredAddress: string
  industryList: string[]
  majorClientList: string[]
  esicSource: string
}

// 能力信息
export interface CapabilityInfo {
  id: number
  companyId: number
  language: string
  categoryFirst: string
  categorySecond: string
  processCapability: string
  productionCap: string
  extIds: number[]
  aiReport: string
  createTime: string
}

// 质量认证信息
export interface QualityCertInfo {
  id: number
  companyId: number
  qualityType: string
  validStartTime: string
  validEndTime: string
  createTime: string
}

// 管理系统信息
export interface ManagementSysInfo {
  id: number
  companyId: number
  name: string
  brand: string
  description: string
  createTime: string
}

// 相册信息
export interface AlbumInfo {
  id: number
  companyId: number
  businessId: number
  businessType: string
  fileId: number
  url: string
  createTime: string
}

// 公司详情响应数据
export interface CompanyDetailData {
  companyInfo: CompanyInfo
  businessInfo: BusinessInfo
  capabilityVOS: CapabilityInfo[]
  qualityCetiList: QualityCertInfo[]
  managementSysList: ManagementSysInfo[]
  albumInfoList: AlbumInfo[]
}

// 请求参数
export interface CompanyDetailParams {
  companyId: number | string
}

// 响应类型
export type CompanyDetailResponse = ApiResponse<CompanyDetailData>

// 首页公司列表响应数据
export interface CompanyHomepageData {
  list: CompanyInfo[]
  total: number
  pageSize: number
  pageNum: number
}

// 首页公司列表请求参数
export interface CompanyHomepageParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  categoryId?: number
  countryId?: number
}

// 首页公司列表响应类型
export type CompanyHomepageResponse = ApiResponse<CompanyHomepageData>

// 买家公司列表响应数据
export interface CompanyBuyerData {
  list: CompanyInfo[]
  total: number
  pageSize: number
  pageNum: number
}

// 买家公司列表请求参数
export interface CompanyBuyerParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  categoryId?: number
  countryId?: number
}

// 买家公司列表响应类型
export type CompanyBuyerResponse = ApiResponse<CompanyBuyerData>

/**
 * 创建用户收藏公司记录(单个添加)
 * @param companyId 公司ID
 * @returns 操作结果
 */
export async function useAddCompareUserRel(companyId: number): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/company/compare-user-rel/add', {
    method: 'POST',
    query: { companyId },
  })
}

/**
 * 删除用户收藏公司记录
 * @param companyIds 公司ID列表
 * @returns 操作结果
 */
export async function useDeleteCompareUserRel(companyIds: string): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/company/compare-user-rel/delete', {
    method: 'DELETE',
    query: { companyIds },
  })
}

/**
 * 获得用户比对公司记录
 * @returns 用户比对公司记录
 */
export async function useGetCompareUserRel(): Promise<ApiResponse<CompareUserRelResp>> {
  return await useProxyRequest<CompareUserRelResp>('admin-api/company/compare-user-rel/get')
}

/**
 * 创建用户收藏公司记录
 * @param companyId 公司ID
 * @returns 操作结果
 */
export async function useSaveCompany(companyId: number): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/company/user-save-rel/save', {
    method: 'POST',
    query: { companyId },
  })
}

/**
 * 删除用户收藏公司记录
 * @param companyId 公司ID
 * @returns 操作结果
 */
export async function useDeleteSavedCompany(companyId: number): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/company/user-save-rel/delete', {
    method: 'DELETE',
    query: { companyId },
  })
}

/**
 * 获得用户收藏公司记录
 * @returns 用户收藏公司记录
 */
export async function useGetSavedCompanies(): Promise<ApiResponse<UserSaveRelResp>> {
  return await useProxyRequest<UserSaveRelResp>('admin-api/company/user-save-rel/get')
}

/**
 * 使用组合式API管理用户比对公司记录
 * 适合在组件和页面中使用
 */
export function useCompareUserRel() {
  const compareUserRel = ref<CompareUserRelResp | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 加载用户比对公司记录
  async function loadCompareUserRel() {
    loading.value = true
    error.value = null

    try {
      const response = await useGetCompareUserRel()

      if (response.code === 0 && response.data) {
        compareUserRel.value = response.data
      }
      else {
        error.value = response.msg
      }
    }
    catch (err: any) {
      error.value = err.message || '获取用户比对公司记录失败'
      console.error('获取用户比对公司记录错误:', err)
    }
    finally {
      loading.value = false
    }
  }

  // 添加公司到比对记录
  async function addCompany(companyId: number) {
    try {
      const response = await useAddCompareUserRel(companyId)
      if (response.code === 0) {
        // 重新加载数据
        await loadCompareUserRel()
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '添加公司到比对记录失败'
      console.error('添加公司到比对记录错误:', err)
      return false
    }
  }

  // 从比对记录中删除公司
  async function removeCompanies(companyIds: number[]) {
    try {
      const response = await useDeleteCompareUserRel(companyIds.join(','))
      if (response.code === 0) {
        // 重新加载数据
        await loadCompareUserRel()
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '从比对记录中删除公司失败'
      console.error('从比对记录中删除公司错误:', err)
      return false
    }
  }

  return {
    compareUserRel,
    loading,
    error,
    loadCompareUserRel,
    addCompany,
    removeCompanies,
  }
}

/**
 * 使用组合式API管理用户收藏公司记录
 * 适合在组件和页面中使用
 */
export function useSavedCompanies() {
  const savedCompanies = ref<UserSaveRelResp | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 加载用户收藏公司记录
  async function loadSavedCompanies() {
    loading.value = true
    error.value = null

    try {
      const response = await useGetSavedCompanies()

      if (response.code === 0 && response.data) {
        savedCompanies.value = response.data
      }
      else {
        error.value = response.msg
      }
    }
    catch (err: any) {
      error.value = err.message || '获取用户收藏公司记录失败'
      console.error('获取用户收藏公司记录错误:', err)
    }
    finally {
      loading.value = false
    }
  }

  // 收藏公司
  async function saveCompany(companyId: number) {
    try {
      const response = await useSaveCompany(companyId)
      if (response.code === 0) {
        // 重新加载数据
        await loadSavedCompanies()
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '收藏公司失败'
      console.error('收藏公司错误:', err)
      return false
    }
  }

  // 取消收藏公司
  async function removeSavedCompany(companyId: number) {
    try {
      const response = await useDeleteSavedCompany(companyId)
      if (response.code === 0) {
        // 重新加载数据
        await loadSavedCompanies()
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '取消收藏公司失败'
      console.error('取消收藏公司错误:', err)
      return false
    }
  }

  // 检查公司是否已收藏
  function isCompanySaved(companyId: number): boolean {
    if (!savedCompanies.value || !savedCompanies.value.companyId) {
      return false
    }
    return savedCompanies.value.companyId.includes(companyId)
  }

  return {
    savedCompanies,
    loading,
    error,
    loadSavedCompanies,
    saveCompany,
    removeSavedCompany,
    isCompanySaved,
  }
}

/**
 * 获取公司基本信息详情
 * @param params 请求参数，包含公司ID
 * @returns 公司详情信息
 */
export async function useCompanyDetail(params: CompanyDetailParams): Promise<CompanyDetailResponse> {
  return await useProxyRequest<CompanyDetailData>(`admin-api/company/base-info/detail?companyId=${params.companyId}`, {
    method: 'GET',
  })
}

/**
 * 获取首页公司列表
 * @param params 请求参数
 * @returns 首页公司列表
 */
export async function useCompanyHomepage(params: CompanyHomepageParams = {}): Promise<CompanyHomepageResponse> {
  return await useProxyRequest<CompanyHomepageData>('admin-api/company/base-info/homepage', {
    method: 'GET',
    query: params,
  })
}

/**
 * 获取买家公司列表
 * @param params 请求参数
 * @returns 买家公司列表
 */
export async function useCompanyBuyer(params: CompanyBuyerParams = {}): Promise<CompanyBuyerResponse> {
  return await useProxyRequest<CompanyBuyerData>('admin-api/company/base-info/buyer', {
    method: 'GET',
    query: params,
  })
}

/**
 * 公司 tag 模糊搜索
 * @param fuzzyText 模糊搜索文本
 * @returns tag 列表
 */
export async function useCompanyTagSearch(fuzzyText: string): Promise<ApiResponse<string[]>> {
  return await useProxyRequest<string[]>('admin-api/company/tag-i18n/search', {
    method: 'GET',
    query: { fuzzyText },
  })
}

/**
 * 公司信息组合式API
 * 提供公司信息的状态管理和方法
 */
export function useCompanyInfo() {
  // 公司信息状态
  const companyInfo = ref<CompanyInfo | null>(null)
  const businessInfo = ref<BusinessInfo | null>(null)
  const capabilities = ref<CapabilityInfo[]>([])
  const qualityCerts = ref<QualityCertInfo[]>([])
  const managementSystems = ref<ManagementSysInfo[]>([])
  const albumInfos = ref<AlbumInfo[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取公司详情
  async function fetchCompanyDetail(companyId: number | string) {
    loading.value = true
    error.value = null

    try {
      const response = await useCompanyDetail({ companyId })

      if (response.code === 0) {
        companyInfo.value = response.data.companyInfo
        businessInfo.value = response.data.businessInfo
        capabilities.value = response.data.capabilityVOS
        qualityCerts.value = response.data.qualityCetiList
        managementSystems.value = response.data.managementSysList
        albumInfos.value = response.data.albumInfoList
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || 'Failed to fetch company details'
      console.error('Company detail fetch error:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 重置状态
  function reset() {
    companyInfo.value = null
    businessInfo.value = null
    capabilities.value = []
    qualityCerts.value = []
    managementSystems.value = []
    albumInfos.value = []
    error.value = null
  }

  return {
    companyInfo,
    businessInfo,
    capabilities,
    qualityCerts,
    managementSystems,
    albumInfos,
    loading,
    error,
    fetchCompanyDetail,
    reset,
  }
}
