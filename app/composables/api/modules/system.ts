import type { ApiResponse, PageParams, PageResult, SystemApi } from '../types'
/**
 * 系统模块 API
 * 包含字典、配置、菜单等系统管理相关接口
 */
import { useProxyRequest } from '../index'

/**
 * 获取字典类型列表
 * @param params 分页查询参数
 * @returns 字典类型列表
 */
export async function useDictTypeList(params: PageParams): Promise<ApiResponse<PageResult<SystemApi.DictType>>> {
  return await useProxyRequest<PageResult<SystemApi.DictType>>('admin-api/system/dict-type/list', {
    method: 'GET',
    query: params,
  })
}

/**
 * 获取全部字典类型列表
 * @param locale 语言环境，可选参数
 * @returns 全部字典类型列表
 */
export async function useDictTypeListAllSimple(locale?: string): Promise<ApiResponse<SystemApi.DictType[]>> {
  return await useProxyRequest<SystemApi.DictType[]>('admin-api/system/dict-type/list-all-simple', {
    method: 'GET',
    headers: locale ? { locale } : undefined,
  })
}

/**
 * 获取指定字典类型的数据
 * @param dictType 字典类型
 * @returns 字典数据列表
 */
export async function useDictDataList(): Promise<ApiResponse<SystemApi.DictData[]>> {
  return await useProxyRequest<SystemApi.DictData[]>('admin-api/system/dict-data/list-all-simple', {
    method: 'GET',
  })
}

/**
 * 创建字典类型
 * @param data 字典类型数据
 * @returns 创建结果
 */
export async function createDictType(data: Omit<SystemApi.DictType, 'id'>): Promise<ApiResponse<number>> {
  return await useProxyRequest<number>('admin-api/system/dict-type/create', {
    method: 'POST',
    body: data,
  })
}

/**
 * 更新字典类型
 * @param data 字典类型数据
 * @returns 更新结果
 */
export async function updateDictType(data: SystemApi.DictType): Promise<ApiResponse<null>> {
  return await useProxyRequest<null>('admin-api/system/dict-type/update', {
    method: 'PUT',
    body: data,
  })
}

/**
 * 删除字典类型
 * @param id 字典类型ID
 * @returns 删除结果
 */
export async function deleteDictType(id: number): Promise<ApiResponse<null>> {
  return await useProxyRequest<null>('admin-api/system/dict-type/delete', {
    method: 'DELETE',
    query: { id },
  })
}

/**
 * 创建字典数据
 * @param data 字典数据
 * @returns 创建结果
 */
export async function createDictData(data: Omit<SystemApi.DictData, 'id'>): Promise<ApiResponse<number>> {
  return await useProxyRequest<number>('admin-api/system/dict-data/create', {
    method: 'POST',
    body: data,
  })
}

/**
 * 更新字典数据
 * @param data 字典数据
 * @returns 更新结果
 */
export async function updateDictData(data: SystemApi.DictData): Promise<ApiResponse<null>> {
  return await useProxyRequest<null>('admin-api/system/dict-data/update', {
    method: 'PUT',
    body: data,
  })
}

/**
 * 删除字典数据
 * @param id 字典数据ID
 * @returns 删除结果
 */
export async function deleteDictData(id: number): Promise<ApiResponse<null>> {
  return await useProxyRequest<null>('admin-api/system/dict-data/delete', {
    method: 'DELETE',
    query: { id },
  })
}

/**
 * 使用字典数据的组合式API
 * @param dictType 字典类型
 * @returns 字典数据和操作方法
 */
export function useDictData() {
  const dictData = ref<SystemApi.DictData[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 加载字典数据
  async function loadDictData() {
    loading.value = true
    error.value = null

    try {
      const response = await useDictDataList()
      if (response.code === 0) {
        dictData.value = response.data || []
      }
      else {
        error.value = response.msg
      }
    }
    catch (err: any) {
      error.value = err.message || '获取字典数据失败'
      console.error('获取字典数据错误:', err)
    }
    finally {
      loading.value = false
    }
  }

  // 通过值获取标签
  function getLabelByValue(value: string): string {
    const item = dictData.value.find(item => item.value === value)
    return item ? item.label : ''
  }

  // 获取选项列表，适用于下拉框等
  function getOptions() {
    return dictData.value.map(item => ({
      label: item.label,
      value: item.value,
    }))
  }

  // 初始加载
  onMounted(() => {
    loadDictData()
  })

  return {
    dictData,
    loading,
    error,
    loadDictData,
    getLabelByValue,
    getOptions,
  }
}
