/**
 * 搜索模块 API
 * 专门用于处理搜索相关接口
 */
import type { SearchApi } from '../types'
import { acceptHMRUpdate, defineStore } from 'pinia'
import { useProxyRequest } from '../index'

/**
 * 用户搜索辅助
 * @param params 搜索参数
 * @returns 搜索结果
 */
export async function useUserSearchAssist(params: SearchApi.UserSearchAssistParams): Promise<SearchApi.UserSearchAssistResponse> {
  return await useProxyRequest<SearchApi.SearchResultData>('admin-api/search/user-search/do-search-assist', {
    method: 'POST',
    body: params,
  })
}

/**
 * 搜索状态管理 Store
 * 提供全局搜索状态管理
 */
export const useSearchStore = defineStore('search', () => {
  // 全局搜索状态
  const searchText = ref('')
  const lastSearchedText = ref('') // 存储最后一次执行搜索时的搜索词
  const from = ref(1)
  const size = ref(25)
  const assistPreciseReqList = ref<SearchApi.AssistPreciseReq[]>([])
  const searchResults = ref<SearchApi.SupplierInfo[]>([])
  const total = ref(0)
  const totalVetted = ref(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 筛选条件状态
  const selectedCategories = ref<string[]>([])
  const selectedTags = ref<string[]>([])
  const selectedLocations = ref<string[]>([])
  const selectedCertifications = ref<string[]>([])
  const selectedEstablishYears = ref<string[]>([])

  // 计算属性：是否有活跃的筛选条件
  const hasActiveFilters = computed(() => {
    return selectedCategories.value.length > 0
      || selectedTags.value.length > 0
      || selectedLocations.value.length > 0
      || selectedCertifications.value.length > 0
      || selectedEstablishYears.value.length > 0
  })

  // 计算属性：将筛选条件转换为搜索API需要的格式
  const filterConditions = computed(() => {
    const conditions: SearchApi.AssistPreciseReq[] = []

    // 添加分类筛选条件
    selectedCategories.value.forEach((category) => {
      conditions.push({
        entity: 'categories_l',
        value: category,
      })
    })

    // 添加认证筛选条件
    selectedCertifications.value.forEach((certification) => {
      conditions.push({
        entity: 'certification_l',
        value: certification,
      })
    })

    // 添加标签筛选条件
    selectedTags.value.forEach((tag) => {
      conditions.push({
        entity: 'tags_l',
        value: tag,
      })
    })

    // 添加成立年份筛选条件
    selectedEstablishYears.value.forEach((year) => {
      conditions.push({
        entity: 'established_time_l',
        value: year,
      })
    })

    // 添加地区筛选条件
    selectedLocations.value.forEach((location) => {
      conditions.push({
        entity: 'location_l',
        value: location,
      })
    })

    return conditions
  })

  // 筛选条件操作方法

  // 添加分类筛选
  function addCategory(category: string) {
    if (!selectedCategories.value.includes(category)) {
      selectedCategories.value.push(category)
    }
  }

  // 移除分类筛选
  function removeCategory(category: string) {
    const index = selectedCategories.value.indexOf(category)
    if (index !== -1) {
      selectedCategories.value.splice(index, 1)
    }
  }

  // 添加标签筛选
  function addTag(tag: string) {
    if (!selectedTags.value.includes(tag)) {
      selectedTags.value.push(tag)
    }
  }

  // 移除标签筛选
  function removeTag(tag: string) {
    const index = selectedTags.value.indexOf(tag)
    if (index !== -1) {
      selectedTags.value.splice(index, 1)
    }
  }

  // 添加地区筛选
  function addLocation(location: string) {
    if (!selectedLocations.value.includes(location)) {
      selectedLocations.value.push(location)
    }
  }

  // 移除地区筛选
  function removeLocation(location: string) {
    const index = selectedLocations.value.indexOf(location)
    if (index !== -1) {
      selectedLocations.value.splice(index, 1)
    }
  }

  // 添加认证筛选
  function addCertification(certification: string) {
    if (!selectedCertifications.value.includes(certification)) {
      selectedCertifications.value.push(certification)
    }
  }

  // 移除认证筛选
  function removeCertification(certification: string) {
    const index = selectedCertifications.value.indexOf(certification)
    if (index !== -1) {
      selectedCertifications.value.splice(index, 1)
    }
  }

  // 添加成立年份筛选
  function addEstablishYear(year: string) {
    if (!selectedEstablishYears.value.includes(year)) {
      selectedEstablishYears.value.push(year)
    }
  }

  // 移除成立年份筛选
  function removeEstablishYear(year: string) {
    const index = selectedEstablishYears.value.indexOf(year)
    if (index !== -1) {
      selectedEstablishYears.value.splice(index, 1)
    }
  }

  // 清空所有筛选条件
  function clearAllFilters() {
    selectedCategories.value = []
    selectedTags.value = []
    selectedLocations.value = []
    selectedCertifications.value = []
    selectedEstablishYears.value = []
  }

  // 执行搜索
  async function doSearch(text: string, additionalPreciseReqs: SearchApi.AssistPreciseReq[] = []) {
    loading.value = true
    error.value = null

    try {
      searchText.value = text
      lastSearchedText.value = text // 保存执行搜索时的搜索词

      // 合并筛选条件和额外的精确搜索条件
      const allPreciseReqs = [...filterConditions.value, ...additionalPreciseReqs]
      assistPreciseReqList.value = allPreciseReqs

      const response = await useUserSearchAssist({
        searchText: text,
        from: (from.value - 1) * size.value,
        size: size.value,
        assistPreciseReqList: allPreciseReqs,
      })

      if (response.code === 0) {
        // response.data.result.forEach((item) => {
        //   item.supplier_images = item.supplier_images?.split(',')
        // })
        searchResults.value = response.data.result
        total.value = response.data.total
        totalVetted.value = response.data.totalVetted
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || 'Search failed'
      console.error('Search error:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 无缓存搜索
  async function doOriginSearch(text: string, additionalPreciseReqs: SearchApi.AssistPreciseReq[] = []) {
    loading.value = true
    error.value = null

    try {
      const response = await useUserSearchAssist({
        searchText: text,
        from: (from.value - 1) * size.value,
        size: size.value,
        assistPreciseReqList: additionalPreciseReqs,
      })

      if (response.code === 0) {
        response.data.result.forEach((item) => {
          item.supplier_images = item.supplier_images?.split(',')
        })
        searchResults.value = response.data.result
        total.value = response.data.total
        totalVetted.value = response.data.totalVetted
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || 'Search failed'
      console.error('Search error:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 添加精确搜索条件
  function addPreciseReq(entity: string, value: string, source: number) {
    assistPreciseReqList.value.push({ entity, value, source })
  }

  // 移除精确搜索条件
  function removePreciseReq(index: number) {
    assistPreciseReqList.value.splice(index, 1)
  }

  // 清空搜索条件
  function clearPreciseReqs() {
    assistPreciseReqList.value = []
  }

  // 重置搜索状态
  function reset() {
    searchText.value = ''
    lastSearchedText.value = ''
    assistPreciseReqList.value = []
    searchResults.value = []
    total.value = 0
    totalVetted.value = 0
    error.value = null
    // 重置筛选条件
    clearAllFilters()
  }

  return {
    // 搜索状态
    searchText,
    lastSearchedText,
    assistPreciseReqList,
    searchResults,
    total,
    totalVetted,
    loading,
    error,
    from,
    size,

    // 筛选条件状态
    selectedCategories,
    selectedTags,
    selectedLocations,
    selectedCertifications,
    selectedEstablishYears,
    hasActiveFilters,
    filterConditions,

    // 搜索方法
    doSearch,
    doOriginSearch,
    // 筛选条件操作方法
    addCategory,
    removeCategory,
    addTag,
    removeTag,
    addLocation,
    removeLocation,
    addCertification,
    removeCertification,
    addEstablishYear,
    removeEstablishYear,
    clearAllFilters,

    // 原有方法
    addPreciseReq,
    removePreciseReq,
    clearPreciseReqs,
    reset,
  }
})

// 支持HMR（热模块替换）
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useSearchStore, import.meta.hot))
}

/**
 * 搜索组合式API
 * 提供搜索功能的状态管理和方法
 * 兼容旧版API，内部使用全局状态
 */
export function useSearchFlow() {
  const searchStore = useSearchStore()
  return searchStore
}
