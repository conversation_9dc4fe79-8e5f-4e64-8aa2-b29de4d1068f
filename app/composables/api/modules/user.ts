/**
 * 用户模块 API
 * 包含用户认证、个人信息等相关接口
 */
import type { ApiResponse, UserApi } from '../types'
import { useProxyRequest } from '../index'

/**
 * 用户登录
 * @param params 登录参数
 * @returns 登录结果
 */
export async function useUserLogin(params: UserApi.LoginParams): Promise<ApiResponse<UserApi.PermissionInfo>> {
  return await useProxyRequest<UserApi.PermissionInfo>('admin-api/system/auth/login', {
    method: 'POST',
    body: params,
  })
}

/**
 * 用户登出
 * @returns 登出结果
 */
export async function useUserLogout(): Promise<ApiResponse<null>> {
  return await useProxyRequest('admin-api/system/auth/logout', {
    method: 'POST',
  })
}

/**
 * 获取用户权限信息
 * @returns 用户权限信息
 */
export async function useUserPermissions(): Promise<ApiResponse<UserApi.PermissionInfo>> {
  return await useProxyRequest<UserApi.PermissionInfo>('admin-api/system/auth/get-permission-info')
}

/**
 * 刷新访问令牌
 * @returns 新令牌信息
 */
export async function useRefreshToken(): Promise<ApiResponse<{ accessToken: string }>> {
  return await useProxyRequest<{ accessToken: string }>('admin-api/system/auth/refresh-token', {
    method: 'POST',
  })
}

/**
 * 获取用户个人信息
 * @returns 用户个人信息
 */
export async function useUserProfile(): Promise<ApiResponse<UserApi.UserProfileInfo>> {
  return await useProxyRequest<UserApi.UserProfileInfo>('admin-api/system/user/profile/get')
}

/**
 * 更新用户个人信息
 * @param params 更新参数
 * @returns 更新结果
 */
export async function useUpdateUserProfile(params: UserApi.UpdateUserProfileParams): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/system/user/profile/update', {
    method: 'PUT',
    body: params,
  })
}

/**
 * 获取并存储当前用户信息
 * 使用组合式API，适合在组件和页面中使用
 */
export function useCurrentUser() {
  // 使用ref存储用户信息
  const user = ref<UserApi.UserProfileInfo | null>(null)
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 加载用户信息
  async function loadUserInfo() {
    loading.value = true
    error.value = null

    try {
      const response = await useUserProfile()

      if (response.code === 0 && response.data) {
        user.value = response.data
        // 从权限信息中获取角色和权限
        // const permissionResponse = await useUserPermissions()
        // if (permissionResponse.code === 0 && permissionResponse.data) {
        //   permissions.value = permissionResponse.data.permissions
        //   roles.value = permissionResponse.data.roles
        // }
      }
      else {
        error.value = response.msg
      }
    }
    catch (err: any) {
      error.value = err.message || '获取用户信息失败'
      console.error('获取用户信息错误:', err)
    }
    finally {
      loading.value = false
    }
  }

  // 检查用户是否有指定权限
  function hasPermission(permission: string): boolean {
    return permissions.value.includes(permission)
  }

  // 检查用户是否有指定角色
  function hasRole(role: string): boolean {
    return roles.value.includes(role)
  }

  // 清除用户信息
  function clearUserInfo() {
    user.value = null
    permissions.value = []
    roles.value = []
  }

  return {
    user,
    permissions,
    roles,
    loading,
    error,
    loadUserInfo,
    hasPermission,
    hasRole,
    clearUserInfo,
  }
}
