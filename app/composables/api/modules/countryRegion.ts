import type { ApiResponse, CountryRegionApi, PageParams, PageResult } from '../types'

import { useProxyRequest } from '../index'

/**
 * 获取国家地区分页列表
 */
export async function useCountryRegionPage(params: PageParams & { nameLocalId?: string, name?: string, type?: string, parentId?: number, level?: number, code?: string, keyPath?: string, keyId?: string, sort?: number, status?: boolean, createTime?: string[] }): Promise<ApiResponse<PageResult<CountryRegionApi.CountryRegion>>> {
  return await useProxyRequest<PageResult<CountryRegionApi.CountryRegion>>('admin-api/system/country-region/page', {
    method: 'GET',
    query: params,
  })
}

/**
 * 获取单个国家地区详情
 */
export async function useCountryRegionDetail(id: number): Promise<ApiResponse<CountryRegionApi.CountryRegion>> {
  return await useProxyRequest<CountryRegionApi.CountryRegion>('admin-api/system/country-region/get', {
    method: 'GET',
    query: { id },
  })
}

/**
 * 获取所有国家地区列表
 */
export async function useCountryRegionList(ids?: number[], status?: number): Promise<ApiResponse<CountryRegionApi.CountryRegion[]>> {
  return await useProxyRequest<CountryRegionApi.CountryRegion[]>('admin-api/system/country-region/list-all', {
    method: 'GET',
  })
}
