/**
 * 文件模块 API
 * 专门用于处理文件上传和存储相关接口
 */
import type { ApiResponse } from '../types'
import { useProxyRequest } from '../index'
import type {UploadRequestOptions} from "element-plus";

/**
 * 文件预签名URL响应接口
 */
export interface FilePresignedUrlRespVO {
  url: string
  params: Record<string, string>
  expirationTime: number
}

/**
 * 文件创建请求接口
 */
export interface FileCreateReqVO {
  path: string
  name: string
  url: string
  size: number
  type?: string
}

/**
 * 上传文件
 * @param file 文件对象
 * @param path 存储路径
 * @returns 文件访问URL
 */

export async function useUploadFile(options: UploadRequestOptions): Promise<ApiResponse<string>> {
  const formData = new FormData()
  formData.append('file', options.file)

  return await useProxyRequest<string>('admin-api/infra/file/upload', {
    method: 'POST',
    body: formData,
    // 使用FormData时不需要设置Content-Type，浏览器会自动设置

  })
}

/**
 * 获取文件预签名上传URL
 * @param path 存储路径
 * @returns 预签名URL及相关参数
 */
export async function useGetFilePresignedUrl(path: string): Promise<ApiResponse<FilePresignedUrlRespVO>> {
  return await useProxyRequest<FilePresignedUrlRespVO>('admin-api/infra/file/presigned-url', {
    method: 'GET',
    query: { path },
  })
}

/**
 * 创建文件记录（预签名上传完成后）
 * @param createReqVO 文件创建信息
 * @returns 文件ID
 */
export async function useCreateFile(createReqVO: FileCreateReqVO): Promise<ApiResponse<number>> {
  return await useProxyRequest<number>('admin-api/infra/file/create', {
    method: 'POST',
    body: createReqVO,
  })
}


