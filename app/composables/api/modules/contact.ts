import type { ApiResponse } from '../types'
/**
 * 联系模块 API
 * 处理联系表单提交和相关功能
 */
import { useProxyRequest } from '../index'

/**
 * 联系ESiC表单提交参数接口
 */
export interface ContactESiCParams {
  projectTitle: string
  message: string
  fileIds: number[]
  firstName?: string
  lastName?: string
  email: string
  company?: string
  suppliers?: number[]
  selectInfo?: string
  cids?: number[]
  tags?: string[]
}

/**
 * 联系ESiC表单提交
 * @param params 表单参数
 * @returns 提交结果
 */
export async function useContactESiC(params: ContactESiCParams): Promise<ApiResponse<any>> {
  return await useProxyRequest<any>('admin-api/contact/esic-record/contact-esic', {
    method: 'POST',
    body: params,
  })
}

/**
 * 比较供应商参数接口
 */
export interface CompareSupplierParams {
  companyIds: number[]
}

/**
 * 比较供应商
 * @param params 比较参数
 * @returns 比较结果
 */
export async function useCompareSupplier(params: CompareSupplierParams): Promise<ApiResponse<any>> {
  return await useProxyRequest<any>('admin-api/contact/esic-record/compare-supplier', {
    method: 'POST',
    query: params,
  })
}
