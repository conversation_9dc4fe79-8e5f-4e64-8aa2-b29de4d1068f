/**
 * 认证模块 API
 * 专门用于处理前端登录注册流程相关接口
 */
import type { ApiResponse, UserApi } from '../types'
import { getApiData, useProxyRequest } from '../index'

/**
 * 发送邮箱验证码
 * @param email 邮箱地址
 * @param scene 场景类型(5-注册等)
 * @returns 发送结果
 */
export async function useSendMailCode(email: string, scene: number = 5): Promise<ApiResponse<null>> {
  return await useProxyRequest<null>('admin-api/system/auth/send-mail-code', {
    method: 'POST',
    body: {
      mail: email,
      scene,
    },
  })
}

/**
 * 验证邮箱验证码
 * @param email 邮箱地址
 * @param code 验证码
 * @returns 验证结果
 */
export async function useValidateMailCode(email: string, code: string): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/system/auth/validate-mail-code', {
    method: 'POST',
    body: {
      mail: email,
      code,
    },
  })
}

/**
 * 邮箱注册
 * @param email 邮箱地址
 * @param password 密码
 * @param nickname 昵称
 * @returns 注册结果
 */
export async function useRegister(email: string, password: string, nickname: string) {
  return await useProxyRequest<UserApi.UserInfo>('admin-api/system/auth/register2', {
    method: 'POST',
    body: {
      mail: email,
      password,
      nickname,
    },
  })
}

/**
 * 重置密码
 * @param email 邮箱地址
 * @param password 新密码
 * @returns 重置结果
 */
export async function useResetPassword(email: string, password: string): Promise<ApiResponse<null>> {
  return await useProxyRequest<null>('admin-api/system/auth/reset-password2', {
    method: 'POST',
    body: {
      mail: email,
      password,
    },
  })
}

/**
 * 邮箱登录
 * @param email 邮箱地址
 * @param password 密码
 * @returns 登录结果
 */
export async function useLogin(email: string, password: string): Promise<ApiResponse<UserApi.PermissionInfo>> {
  return await useProxyRequest<UserApi.PermissionInfo>('admin-api/system/auth/login2', {
    method: 'POST',
    body: {
      mail: email,
      password,
    },
  })
}

/**
 * 用户登出
 * @returns 登出结果
 */
export async function useLogout(): Promise<ApiResponse<null>> {
  return await useProxyRequest('admin-api/system/auth/logout', {
    method: 'POST',
  })
}

/**
 * 前端注册流程组合式API
 * 提供完整的注册流程状态和方法
 */
export function useRegisterFlow() {
  // 流程状态
  const step = ref(1) // 1-输入邮箱, 2-验证码验证, 3-设置密码, 4-完成
  const email = ref('')
  const code = ref('')
  const password = ref('')
  const nickname = ref('')
  const loading = ref(false)
  const error = ref<string | null>(null)
  const success = ref(false)

  // 发送验证码
  async function sendCode(mail: string) {
    loading.value = true
    error.value = null

    try {
      email.value = mail
      const response = await useSendMailCode(mail)

      if (response.code === 0) {
        step.value = 2 // 进入验证码验证步骤
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '发送验证码失败'
      console.error('发送验证码错误:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 验证验证码
  async function validateCode(inputCode: string) {
    loading.value = true
    error.value = null

    try {
      code.value = inputCode
      const response = await useValidateMailCode(email.value, inputCode)

      if (response.code === 0 && response.data) {
        step.value = 3 // 进入设置密码步骤
        return true
      }
      else {
        error.value = response.msg || '验证码错误'
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '验证码验证失败'
      console.error('验证码验证错误:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 完成注册
  async function completeRegister(inputPassword: string, inputNickname: string) {
    loading.value = true
    error.value = null

    try {
      password.value = inputPassword
      nickname.value = inputNickname

      const response = await useRegister(email.value, inputPassword, inputNickname)

      if (response.code === 0) {
        step.value = 4 // 完成注册
        success.value = true
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '注册失败'
      console.error('注册错误:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 重置表单
  function reset() {
    step.value = 1
    email.value = ''
    code.value = ''
    password.value = ''
    nickname.value = ''
    error.value = null
    success.value = false
  }

  return {
    step,
    email,
    code,
    password,
    nickname,
    loading,
    error,
    success,
    sendCode,
    validateCode,
    completeRegister,
    reset,
  }
}

/**
 * 前端忘记密码流程组合式API
 */
export function useResetPasswordFlow() {
  // 流程状态
  const step = ref(1) // 1-输入邮箱, 2-验证码验证, 3-设置新密码, 4-完成
  const email = ref('')
  const code = ref('')
  const password = ref('')
  const loading = ref(false)
  const error = ref<string | null>(null)
  const success = ref(false)

  // 发送验证码
  async function sendCode(mail: string) {
    loading.value = true
    error.value = null

    try {
      email.value = mail
      // 使用场景10表示忘记密码
      const response = await useSendMailCode(mail, 10)

      if (response.code === 0) {
        step.value = 2 // 进入验证码验证步骤
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '发送验证码失败'
      console.error('发送验证码错误:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 验证验证码
  async function validateCode(inputCode: string) {
    loading.value = true
    error.value = null

    try {
      code.value = inputCode
      const response = await useValidateMailCode(email.value, inputCode)

      if (response.code === 0 && response.data) {
        step.value = 3 // 进入设置新密码步骤
        return true
      }
      else {
        error.value = response.msg || '验证码错误'
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '验证码验证失败'
      console.error('验证码验证错误:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 完成密码重置
  async function completeReset(newPassword: string) {
    loading.value = true
    error.value = null

    try {
      password.value = newPassword
      const response = await useResetPassword(email.value, newPassword)

      if (response.code === 0) {
        step.value = 4 // 完成重置
        success.value = true
        return true
      }
      else {
        error.value = response.msg
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || '密码重置失败'
      console.error('密码重置错误:', err)
      return false
    }
    finally {
      loading.value = false
    }
  }

  // 重置表单
  function reset() {
    step.value = 1
    email.value = ''
    code.value = ''
    password.value = ''
    error.value = null
    success.value = false
  }

  return {
    step,
    email,
    code,
    password,
    loading,
    error,
    success,
    sendCode,
    validateCode,
    completeReset,
    reset,
  }
}
