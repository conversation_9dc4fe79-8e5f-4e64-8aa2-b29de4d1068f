import type { ApiResponse, CategoryApi, PageParams, PageResult } from '../types'

import { useProxyRequest } from '../index'

/**
 * 获取品类分页列表
 */
export async function useCategoryList(params: PageParams & { code?: string, parentId?: number, name?: string, brand?: number, status?: number, beginCreateTime?: string, endCreateTime?: string, locale?: string }): Promise<ApiResponse<PageResult<CategoryApi.Category>>> {
  return await useProxyRequest<PageResult<CategoryApi.Category>>('admin-api/system/categories/list', {
    method: 'GET',
    query: params,
  })
}

/**
 * 获取单个品类详情
 */
export async function useCategoryDetail(id: number): Promise<ApiResponse<CategoryApi.Category>> {
  return await useProxyRequest<CategoryApi.Category>('admin-api/system/categories/get', {
    method: 'GET',
    query: { id },
  })
}

/**
 * 创建品类
 */
export async function createCategory(data: Partial<CategoryApi.Category>): Promise<ApiResponse<number>> {
  return await useProxyRequest<number>('admin-api/system/categories/create', {
    method: 'POST',
    body: data,
  })
}

/**
 * 更新品类
 */
export async function updateCategory(data: Partial<CategoryApi.Category> & { id: number }): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/system/categories/update', {
    method: 'PUT',
    body: data,
  })
}

/**
 * 删除品类
 */
export async function deleteCategory(id: number): Promise<ApiResponse<boolean>> {
  return await useProxyRequest<boolean>('admin-api/system/categories/delete', {
    method: 'DELETE',
    query: { id },
  })
}

/**
 * 获取所有品类level
 */
export async function useCategoryLevels(): Promise<ApiResponse<number[]>> {
  return await useProxyRequest<number[]>('admin-api/system/categories/level', {
    method: 'GET',
  })
}

/**
 * 获取缓存品类数据
 */
export async function useCategoryCache(categoryIds?: number[], status?: number, locale = 'zh'): Promise<ApiResponse<CategoryApi.Category[]>> {
  return await useProxyRequest<CategoryApi.Category[]>('admin-api/system/categories/list-category-cache', {
    method: 'GET',
    query: { categoryIds, status },
    headers: { locale },
  })
}
