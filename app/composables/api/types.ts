/**
 * API 类型定义
 * 包含通用响应类型和各模块请求/响应类型
 */

/**
 * 通用API响应格式
 */
export interface ApiResponse<T = any> {
  code: number
  data: T
  msg: string
}

/**
 * 分页请求参数
 */
export interface PageParams {
  pageNo: number
  pageSize: number
  [key: string]: any
}

/**
 * 分页响应结果
 */
export interface PageResult<T> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
}

/**
 * 基础实体字段
 */
export interface BaseEntity {
  id: number | string
  createTime?: string
  updateTime?: string
}

/**
 * 用户模块类型
 */
export namespace UserApi {
  // 登录请求
  export interface LoginParams {
    tenantName: string
    username: string
    password: string
    rememberMe: boolean
  }

  // 邮箱登录请求
  export interface EmailLoginParams {
    mail: string
    password: string
  }

  // 发送邮箱验证码请求
  export interface SendMailCodeParams {
    mail: string
    scene: number // 场景类型，如：5-注册
  }

  // 验证邮箱验证码请求
  export interface ValidateMailCodeParams {
    mail: string
    code: string
  }

  // 邮箱注册请求
  export interface EmailRegisterParams {
    mail: string
    password: string
    nickname: string
  }

  // 重置密码请求
  export interface ResetPasswordParams {
    mail: string
    password: string
  }

  // 更新用户个人信息请求
  export interface UpdateUserProfileParams {
    nickname: string
    email?: string
    mobile?: string
    companyName?: string
    sex?: number
  }

  // 用户个人信息响应
  export interface UserProfileInfo extends BaseEntity {
    id: number
    username: string
    nickname: string
    email?: string
    mobile?: string
    sex?: number
    avatar?: string
    loginIp: string
    loginDate: string
    roles?: RoleInfo[]
    dept?: DeptInfo
    posts?: PostInfo[]
    socialUsers?: SocialUserInfo[]
    // 注意：根据接口文档，没有 companyName 字段
    // 如果需要公司名称，可能需要从其他接口获取或使用其他字段
    companyName?: string // 临时保留，用于向后兼容
  }

  // 角色信息
  export interface RoleInfo {
    id: number
    name: string
    code: string
    sort: number
    status: number
  }

  // 部门信息
  export interface DeptInfo {
    id: number
    name: string
    parentId?: number
    sort: number
    status: number
  }

  // 岗位信息
  export interface PostInfo {
    id: number
    name: string
    code: string
    sort: number
    status: number
  }

  // 社交用户信息
  export interface SocialUserInfo {
    id: number
    type: number
    openid: string
    token: string
    rawTokenInfo: string
    nickname: string
    avatar: string
    rawUserInfo: string
  }

  // 用户信息
  export interface UserInfo extends BaseEntity {
    username: string
    nickname: string
    email: string
    mobile: string
    sex: number
    avatar: string
    status: number
    loginIp: string
    loginDate: string
  }

  // 用户权限信息
  export interface PermissionInfo {
    user: UserInfo
    roles: string[]
    permissions: string[]
    menus: any[]
  }

}

/**
 * 系统管理模块类型
 */
export namespace SystemApi {
  // 字典类型
  export interface DictType extends BaseEntity {
    name: string
    type: string
    status: number
    remark: string
  }

  // 字典数据
  export interface DictData extends BaseEntity {
    extendValue: string
    dictType: string
    sort: number
    label: string
    value: string
    status: number
    colorType: string
    cssClass: string
    remark: string
  }
}

/**
 * API 调用选项
 */
export interface ApiOptions {
  // 是否在请求失败时显示错误通知
  showError?: boolean
  // 自定义错误消息
  errorMessage?: string
  // 自定义请求头
  headers?: Record<string, string>
  // 是否抛出错误
  throwError?: boolean
}

/**
 * 品类模块类型
 */
export namespace CategoryApi {
  export interface Category extends BaseEntity {
    parentId?: number
    code?: string
    name: string
    nameLocalId?: string
    level?: number
    brand?: number
    status?: number
    keyPath?: string
    keyId?: string
    position?: string
    translations?: Translation[]
  }
  export interface Translation {
    translationId: string
    locale: string
    translation: string
    creator?: string
    updater?: string
    id?: number
    createTime?: string
  }
}

/**
 * 国家地区模块类型
 */
export namespace CountryRegionApi {
  export interface CountryRegion extends BaseEntity {
    nameLocalId: string
    name: string
    type: string // country/region
    parentId?: number
    level?: number
    code: string
    keyPath?: string
    keyId?: string
    sort?: number
    status: boolean
    createTime: string
  }
}

/**
 * 搜索模块类型
 */
export namespace SearchApi {
  // 搜索辅助精确请求项
  export interface AssistPreciseReq {
    entity: string
    value: string
    source?: number
  }

  // 用户搜索辅助请求
  export interface UserSearchAssistParams {
    searchText: string
    assistPreciseReqList: AssistPreciseReq[],
    from?: number
    size?: number
  }

  // 供应商信息
  export interface SupplierInfo {
    supplier_images: any
    supplier_id?: number | string
    supplier_name: string
    location: string
    location_l: string
    supplier_logo: string
    vetted: boolean
    established_time: string
    annual_sales: number
    employee_count: number
    industry: string
    major_clients: string
    supplier_pics: string[]
  }

  // 搜索结果数据
  export interface SearchResultData {
    id: number
    total: number
    totalVetted: number
    result: SupplierInfo[]
  }

  // 搜索响应
  export interface UserSearchAssistResponse extends ApiResponse<SearchResultData> {}
}
