import type { CategoryApi, CountryRegionApi } from './api/types'
/**
 * 缓存数据访问组合式函数
 * 提供快捷方法访问缓存数据
 */
import { useCacheStore } from './cache'

/**
 * 刷新系统缓存数据
 * @param dictTypes 需要刷新的字典类型数组
 * @returns 刷新结果和状态
 */
export function useRefreshCache() {
  const cacheStore = useCacheStore()
  const { locale } = useI18n()
  const refreshing = ref(false)

  // 默认字典类型
  const defaultDictTypes = [
    'sys_normal_disable',
    'sys_user_sex',
    'sys_yes_no',
  ]

  // 刷新所有缓存数据
  const refreshAll = async () => {
    refreshing.value = true
    try {
      await cacheStore.initCacheData(defaultDictTypes, locale.value)
      return true
    }
    catch (error) {
      console.error('刷新缓存数据失败:', error)
      return false
    }
    finally {
      refreshing.value = false
    }
  }

  // 刷新字典类型数据
  const refreshDictTypeList = async () => {
    refreshing.value = true
    try {
      await cacheStore.loadDictTypeList()
      return true
    }
    catch (error) {
      console.error('刷新字典类型数据失败:', error)
      return false
    }
    finally {
      refreshing.value = false
    }
  }

  // 刷新所有字典数据
  const refreshAllDictData = async () => {
    refreshing.value = true
    try {
      await cacheStore.loadDictData()
      return true
    }
    catch (error) {
      console.error('刷新所有字典数据失败:', error)
      return false
    }
    finally {
      refreshing.value = false
    }
  }

  // 刷新品类数据
  const refreshCategoryData = async () => {
    refreshing.value = true
    try {
      await cacheStore.loadCategoryData(locale.value)
      return true
    }
    catch (error) {
      console.error('刷新品类数据失败:', error)
      return false
    }
    finally {
      refreshing.value = false
    }
  }

  // 刷新国家地区数据
  const refreshCountryRegionData = async () => {
    refreshing.value = true
    try {
      await cacheStore.loadCountryRegionData()
      return true
    }
    catch (error) {
      console.error('刷新国家地区数据失败:', error)
      return false
    }
    finally {
      refreshing.value = false
    }
  }

  return {
    refreshing,
    refreshAll,
    refreshDictTypeList,
    refreshAllDictData,
    refreshCategoryData,
    refreshCountryRegionData,
  }
}

/**
 * 使用字典数据
 * @param dictType 字典类型
 * @param autoLoad 是否自动加载（如果未加载）
 * @returns 字典数据和相关方法
 */
export function useDict(dictType: string, autoLoad = true) {
  const cacheStore = useCacheStore()

  // 自动加载数据
  if (autoLoad) {
    onMounted(async () => {
      await cacheStore.loadDictData()
    })
  }

  // 获取字典数据
  const dictData = computed(() => cacheStore.getDictData(dictType))

  // 获取字典选项（用于下拉框等）
  const dictOptions = computed(() => {
    return dictData.value.map(item => ({
      label: item.label,
      value: item.value,
      disabled: item.status !== 0,
    }))
  })

  // 根据值获取标签
  const getLabelByValue = (value: string): string => {
    return cacheStore.getDictLabel(dictType, value)
  }

  const getDictData = (dictType: string) => {
    return cacheStore.getDictData(dictType)
  }

  return {
    dictData,
    dictOptions,
    getDictData,
    getLabelByValue,
    loading: computed(() => cacheStore.loading.dict),
    // load: () => cacheStore.loadDictData(dictType),
  }
}

/**
 * 使用品类数据
 * @param autoLoad 是否自动加载（如果未加载）
 * @returns 品类数据和相关方法
 */
export function useCategory(autoLoad = true) {
  const cacheStore = useCacheStore()
  const { locale } = useI18n()

  // 自动加载数据
  if (autoLoad) {
    onMounted(async () => {
      if (cacheStore.categoryCache.length === 0) {
        await cacheStore.loadCategoryData(locale.value)
      }
    })
  }

  // 获取品类列表
  const categoryList = computed(() => cacheStore.categoryCache)

  // 获取品类树
  const categoryTree = computed(() => cacheStore.getCategoryTree)

  // 根据ID获取品类
  const getCategoryById = (id: number | string): CategoryApi.Category | undefined => {
    const numId = typeof id === 'string' ? Number.parseInt(id) : id
    return categoryList.value.find(item => item.id === numId || item.id === id)
  }

  // 根据ID获取品类名称
  const getCategoryNameById = (id: number | string): string => {
    const category = getCategoryById(id)
    return category?.name || ''
  }

  // 根据code获取品类
  const getCategoryByCode = (code: string): CategoryApi.Category | undefined => {
    return categoryList.value.find(item => item.code === code)
  }

  // 根据code获取品类名称
  const getCategoryNameByCode = (code: string): string => {
    const category = getCategoryByCode(code)
    return category?.name || ''
  }

  return {
    categoryList,
    categoryTree,
    getCategoryById,
    getCategoryNameById,
    getCategoryByCode,
    getCategoryNameByCode,
    loading: computed(() => cacheStore.loading.category),
    load: () => cacheStore.loadCategoryData(locale.value),
  }
}

/**
 * 使用国家地区数据
 * @param autoLoad 是否自动加载（如果未加载）
 * @returns 国家地区数据和相关方法
 */
export function useCountryRegion(autoLoad = true) {
  const cacheStore = useCacheStore()

  // 自动加载数据
  if (autoLoad) {
    onMounted(async () => {
      if (cacheStore.countryRegionCache.length === 0) {
        await cacheStore.loadCountryRegionData()
      }
    })
  }

  // 获取国家地区列表
  const countryRegionList = computed(() => cacheStore.countryRegionCache)

  // 获取国家地区树
  const countryRegionTree = computed(() => cacheStore.getCountryRegionTree)

  // 获取国家列表（仅类型为country的数据）
  const countryList = computed(() => {
    return countryRegionList.value.filter(item => item.type === 'country')
  })

  // 获取地区列表（仅类型为region的数据）
  const regionList = computed(() => {
    return countryRegionList.value.filter(item => item.type === 'region')
  })

  // 根据ID获取国家地区
  const getCountryRegionById = (id: number | string): CountryRegionApi.CountryRegion | undefined => {
    const numId = typeof id === 'string' ? Number.parseInt(id) : id
    return countryRegionList.value.find(item => item.id === numId || item.id === id)
  }

  // 根据ID获取国家地区名称
  const getCountryRegionNameById = (id: number | string): string => {
    const region = getCountryRegionById(id)
    return region?.name || ''
  }

  // 获取指定国家的所有地区
  const getRegionsByCountryId = (countryId: number | string): CountryRegionApi.CountryRegion[] => {
    const numId = typeof countryId === 'string' ? Number.parseInt(countryId) : countryId
    return countryRegionList.value.filter(item =>
      item.type === 'region' && item.parentId === numId,
    )
  }

  const getCountryRegionCodeById = (id: number | string): string => {
    const region = getCountryRegionById(id)
    return region?.code || ''
  }
  return {
    countryRegionList,
    countryRegionTree,
    countryList,
    regionList,
    getCountryRegionById,
    getCountryRegionCodeById,
    getCountryRegionNameById,
    getRegionsByCountryId,
    loading: computed(() => cacheStore.loading.countryRegion),
    load: () => cacheStore.loadCountryRegionData(),
  }
}
