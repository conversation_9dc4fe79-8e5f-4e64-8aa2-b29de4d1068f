const DICT_TYPE = {
  AI_GENERATE_MODE: 'ai_generate_mode',
  AI_IMAGE_STATUS: 'ai_image_status',
  AI_MODEL_TYPE: 'ai_model_type',
  AI_MUSIC_STATUS: 'ai_music_status',
  AI_PLATFORM: 'ai_platform',
  AI_WRITE_FORMAT: 'ai_write_format',
  AI_WRITE_LANGUAGE: 'ai_write_language',
  AI_WRITE_LENGTH: 'ai_write_length',
  AI_WRITE_TONE: 'ai_write_tone',
  AI_WRITE_TYPE: 'ai_write_type',
  BPM_MODEL_FORM_TYPE: 'bpm_model_form_type',
  BPM_MODEL_TYPE: 'bpm_model_type',
  BPM_OA_LEAVE_TYPE: 'bpm_oa_leave_type',
  BPM_PROCESS_INSTANCE_STATUS: 'bpm_process_instance_status',
  BPM_PROCESS_LISTENER_TYPE: 'bpm_process_listener_type',
  BPM_PROCESS_LISTENER_VALUE_TYPE: 'bpm_process_listener_value_type',
  BPM_TASK_CANDIDATE_STRATEGY: 'bpm_task_candidate_strategy',
  BPM_TASK_STATUS: 'bpm_task_status',
  BROKERAGE_BANK_NAME: 'brokerage_bank_name',
  BROKERAGE_BIND_MODE: 'brokerage_bind_mode',
  BROKERAGE_ENABLED_CONDITION: 'brokerage_enabled_condition',
  BROKERAGE_RECORD_BIZ_TYPE: 'brokerage_record_biz_type',
  BROKERAGE_RECORD_STATUS: 'brokerage_record_status',
  BROKERAGE_WITHDRAW_STATUS: 'brokerage_withdraw_status',
  BROKERAGE_WITHDRAW_TYPE: 'brokerage_withdraw_type',
  COMMON_STATUS: 'common_status',
  COMPANY_ESTABLISH_YEAR: 'company_establish_year',
  COMPANY_QUALITY_CETI: 'company_quality_ceti',
  CRM_AUDIT_STATUS: 'crm_audit_status',
  CRM_BUSINESS_END_STATUS_TYPE: 'crm_business_end_status_type',
  CRM_CUSTOMER_INDUSTRY: 'crm_customer_industry',
  CRM_CUSTOMER_LEVEL: 'crm_customer_level',
  CRM_CUSTOMER_SOURCE: 'crm_customer_source',
  CRM_FOLLOW_UP_TYPE: 'crm_follow_up_type',
  CRM_PERMISSION_LEVEL: 'crm_permission_level',
  CRM_PRODUCT_STATUS: 'crm_product_status',
  CRM_PRODUCT_UNIT: 'crm_product_unit',
  CRM_RECEIVABLE_RETURN_TYPE: 'crm_receivable_return_type',
  DATE_INTERVAL: 'date_interval',
  ERP_AUDIT_STATUS: 'erp_audit_status',
  ERP_STOCK_RECORD_BIZ_TYPE: 'erp_stock_record_biz_type',
  INFRA_API_ERROR_LOG_PROCESS_STATUS: 'infra_api_error_log_process_status',
  INFRA_BOOLEAN_STRING: 'infra_boolean_string',
  INFRA_CODEGEN_FRONT_TYPE: 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE: 'infra_codegen_scene',
  INFRA_CODEGEN_TEMPLATE_TYPE: 'infra_codegen_template_type',
  INFRA_CONFIG_TYPE: 'infra_config_type',
  INFRA_FILE_STORAGE: 'infra_file_storage',
  INFRA_JOB_LOG_STATUS: 'infra_job_log_status',
  INFRA_JOB_STATUS: 'infra_job_status',
  INFRA_OPERATE_TYPE: 'infra_operate_type',
  IOT_PLUGIN_DEPLOY_TYPE: 'iot_plugin_deploy_type',
  MEMBER_EXPERIENCE_BIZ_TYPE: 'member_experience_biz_type',
  MEMBER_POINT_BIZ_TYPE: 'member_point_biz_type',
  MP_AUTO_REPLY_REQUEST_MATCH: 'mp_auto_reply_request_match',
  MP_MESSAGE_TYPE: 'mp_message_type',
  PAY_CHANNEL_CODE: 'pay_channel_code',
  PAY_NOTIFY_STATUS: 'pay_notify_status',
  PAY_NOTIFY_TYPE: 'pay_notify_type',
  PAY_ORDER_STATUS: 'pay_order_status',
  PAY_REFUND_STATUS: 'pay_refund_status',
  PAY_TRANSFER_STATUS: 'pay_transfer_status',
  PAY_TRANSFER_TYPE: 'pay_transfer_type',
  PRODUCT_SPU_STATUS: 'product_spu_status',
  PROMOTION_ACTIVITY_STATUS: 'promotion_activity_status',
  PROMOTION_BANNER_POSITION: 'promotion_banner_position',
  PROMOTION_BARGAIN_RECORD_STATUS: 'promotion_bargain_record_status',
  PROMOTION_COMBINATION_RECORD_STATUS: 'promotion_combination_record_status',
  PROMOTION_CONDITION_TYPE: 'promotion_condition_type',
  PROMOTION_COUPON_STATUS: 'promotion_coupon_status',
  PROMOTION_COUPON_TAKE_TYPE: 'promotion_coupon_take_type',
  PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE: 'promotion_coupon_template_validity_type',
  PROMOTION_DISCOUNT_TYPE: 'promotion_discount_type',
  PROMOTION_PRODUCT_SCOPE: 'promotion_product_scope',
  SYSTEM_DATA_SCOPE: 'system_data_scope',
  SYSTEM_LOGIN_RESULT: 'system_login_result',
  SYSTEM_LOGIN_TYPE: 'system_login_type',
  SYSTEM_MAIL_SEND_STATUS: 'system_mail_send_status',
  SYSTEM_MENU_TYPE: 'system_menu_type',
  SYSTEM_NOTICE_TYPE: 'system_notice_type',
  SYSTEM_NOTIFY_TEMPLATE_TYPE: 'system_notify_template_type',
  SYSTEM_OAUTH2_GRANT_TYPE: 'system_oauth2_grant_type',
  SYSTEM_ROLE_TYPE: 'system_role_type',
  SYSTEM_SMS_CHANNEL_CODE: 'system_sms_channel_code',
  SYSTEM_SMS_RECEIVE_STATUS: 'system_sms_receive_status',
  SYSTEM_SMS_SEND_STATUS: 'system_sms_send_status',
  SYSTEM_SMS_TEMPLATE_TYPE: 'system_sms_template_type',
  SYSTEM_SOCIAL_TYPE: 'system_social_type',
  SYSTEM_USER_SEX: 'system_user_sex',
  TERMINAL: 'terminal',
  TRADE_AFTER_SALE_STATUS: 'trade_after_sale_status',
  TRADE_AFTER_SALE_TYPE: 'trade_after_sale_type',
  TRADE_AFTER_SALE_WAY: 'trade_after_sale_way',
  TRADE_DELIVERY_EXPRESS_CHARGE_MODE: 'trade_delivery_express_charge_mode',
  TRADE_DELIVERY_TYPE: 'trade_delivery_type',
  TRADE_ORDER_ITEM_AFTER_SALE_STATUS: 'trade_order_item_after_sale_status',
  TRADE_ORDER_STATUS: 'trade_order_status',
  TRADE_ORDER_TYPE: 'trade_order_type',
  USER_TYPE: 'user_type',
  COMPANY_M_ATTRIBUTE_LABEL: 'company_m_attribute_label',
  SAAS_COMPANY_INDUSTRIES_INDUSTRY_CODE: 'SAAS_company_industries_industry_code',
  SAAS_COMPANY_BUSINESS_TYPES_BUSINESS_TYPE: 'SAAS_company_business_types_business_type',
  SAAS_UNIT_AREA: 'SAAS_unit_area',
  SAAS_COMPANY_QUALIFICATIONS_TYPE_QUALITY: 'SAAS_company_qualifications_type.QUALITY',
  BOLE_SUPPLIER_TYPE: 'bole_supplier_type',
}

export default DICT_TYPE
