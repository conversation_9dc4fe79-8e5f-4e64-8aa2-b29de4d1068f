import { defineNuxtPlugin, navigateTo } from '#app'
import { useUserStore } from '~/composables/user'

// 扩展 Error 接口以包含 response 属性
declare global {
  interface Error {
    response?: any
  }
}

export default defineNuxtPlugin(async (nuxtApp) => {
  // const config = useRuntimeConfig() // config 未使用，暂时注释

  // 全局token刷新锁，防止并发刷新
  let isRefreshing = false
  let refreshPromise: Promise<boolean> | null = null

  // 刷新token的简单函数，不处理重试
  async function refreshToken(): Promise<boolean> {
    // 如果正在刷新，等待当前刷新完成
    if (isRefreshing && refreshPromise) {
      console.warn('🔄 Token正在刷新中，等待完成...')
      return await refreshPromise
    }

    // 设置刷新锁
    isRefreshing = true
    console.warn('🔑🔑🔑 开始刷新Token...')

    // 创建刷新Promise
    refreshPromise = (async () => {
      try {
        // 直接调用刷新接口
        const result = await $fetch('/api/auth/refresh-token', {
          method: 'POST',
        })

        console.warn('✅✅✅ Token刷新结果:', result)
        // 修复：刷新成功时返回true，失败时返回false
        return result.code === 0
      }
      catch (error: any) {
        console.error('❌❌❌ Token刷新失败:', error)
        return false
      }
      finally {
        // 释放刷新锁
        isRefreshing = false
        refreshPromise = null
      }
    })()

    return await refreshPromise
  }

  // 创建增强版的fetch实例
  const apiFetch = $fetch.create({
    // 请求前钩子
    onRequest({ request }) {
    },

    // 处理响应
    async onResponse({ request, response }) {
      // 检查是否是token过期情况 (HTTP 200, code 401)
      if (response.status === 200 && response._data?.code === 401) {
        console.warn('⚠️⚠️⚠️ 检测到Token过期 (code=401)')

        // 不处理刷新token的请求
        if (request.toString().includes('/api/auth/refresh-token')) {
          console.warn('⏭️ 跳过刷新Token请求的处理')
          return
        }

        try {
          // 尝试刷新token
          const refreshed = await refreshToken()

          if (refreshed) {
            console.warn('🔄 Token已刷新，可重试请求')

            // 刷新成功后，抛出特殊错误码，调用者需要重试
            const error = new Error('Token已刷新，请重试请求')
            error.response = { code: 499, msg: 'Token已刷新，请重试', data: null }
            throw error
          }
          else {
            // 刷新失败，用户需要重新登录
            const userStore = useUserStore()
            userStore.clearUserInfo()

            if (import.meta.client) {
              console.warn('🚪 重定向到登录页')
              navigateTo('/')
            }

            const error = new Error('登录已过期，请重新登录')
            error.response = { code: 401, msg: '登录已过期，请重新登录', data: null }
            throw error
          }
        }
        catch (error: any) {
          if (error.response?.code === 499) {
            // 特殊错误码，向上抛出
            throw error
          }

          console.error('❌ 处理Token过期失败:', error)
          const err = new Error('登录状态已失效')
          err.response = { code: 401, msg: '登录状态已失效', data: null }
          throw err
        }
      }
      // 处理其他业务错误 (code非0)

      else if (response.status === 200 && response._data?.code !== 0) {
        const error = new Error(response._data?.msg || '请求失败')
        error.response = response._data
        throw error
      }
    },

    // 处理HTTP错误
    async onResponseError({ request, response }): Promise<void> {
      console.error('🚨 HTTP错误:', request.toString(), response.status)
    },
  })

  // 将apiFetch注入应用
  nuxtApp.provide('apiFetch', apiFetch)

  // 全局访问方便调试
  if (import.meta.client) {
    // @ts-expect-error 全局扩展window
    window.$apiFetch = apiFetch

    // 暴露刷新token函数方便手动测试
    // @ts-expect-error 全局扩展window
    window.$refreshToken = refreshToken
  }

  // 启用客户端初始状态恢复
  if (import.meta.client) {
    try {
      const userStore = useUserStore()
      if (!userStore.isLoggedIn) {
        const permissionRes = await apiFetch('/api/auth/get-permission-info')
        if (permissionRes.code === 0 && permissionRes.data) {
          userStore.setPermissionInfo(permissionRes.data)
          userStore.setToken('logged-in')
        }
      }
    }
    catch (error) {
      console.error('初始状态恢复失败:', error)
    }
  }
})
