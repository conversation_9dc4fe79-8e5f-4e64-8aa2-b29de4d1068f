/**
 * 认证中间件
 * 负责检查用户是否登录，并根据路由要求控制访问权限
 */
export default defineNuxtRouteMiddleware(async (to, _from) => {
  const nuxtApp = useNuxtApp()
  const userStore = useUserStore()
  const apiFetch = nuxtApp.$apiFetch as <T = any>(path: string, opts?: any) => Promise<T>

  // 如果用户已在客户端登录，则直接返回(已有token状态)
  if (import.meta.client && userStore.isLoggedIn) {
    return
  }

  // 获取用户权限信息（已登录状态检查）
  // 这里使用apiFetch，它已经内置了token过期自动刷新的功能
  try {
    const permissionRes = await apiFetch<{
      code: number
      data: any
      msg: string
    }>('/api/auth/get-permission-info')

    // 如果成功获取用户信息，更新store
    if (permissionRes.code === 0 && permissionRes.data) {
      userStore.setPermissionInfo(permissionRes.data)
      userStore.setToken('logged-in') // 设置虚拟token标识登录状态
      return // 已登录，继续访问
    }
  }
  catch (error) {
    console.error('认证检查失败:', error)
    // apiFetch已处理token刷新，如果仍失败，说明刷新token也失败了
  }

  // 如果需要强制登录的路由
  const requiresAuth = to.meta.requiresAuth

  // 如果当前路由需要认证但用户未登录
  if (requiresAuth && !userStore.isLoggedIn) {
    // 清除用户状态
    userStore.clearUserInfo()

    // 保存用户想要访问的url，以便登录后重定向
    const redirectPath = to.fullPath

    // 重定向到登录页
    return navigateTo({
      path: '/',
      query: redirectPath !== '/' ? { redirect: redirectPath } : undefined,
    })
  }
})
