/**
 * 全局缓存数据初始化中间件
 * 在路由导航时加载系统缓存数据
 * 通过.global后缀使其应用于所有路由
 */

export default defineNuxtRouteMiddleware(async (to) => {
  if (import.meta.server)
    return
  // 获取nuxt应用实例
  const nuxtApp = useNuxtApp()

  // 从nuxtApp实例获取store
  const pinia = nuxtApp.$pinia
  if (!pinia) {
    console.error('Pinia 实例不可用')
    return
  }

  try {
    // 动态导入模块，确保在合适的上下文中执行
    const { useCacheStore } = await import('~/composables/cache')
    const store = useCacheStore()
    // 常用的字典类型列表

    // 获取当前语言
    let locale = 'en-US'
    if (nuxtApp.$i18n) {
      locale = nuxtApp.$i18n.locale.value
    }
    // 初始化缓存数据
    if (!store.isAllLoaded && !store.isLoading) {
      await store.initCacheData(undefined, locale)
    }
  }
  catch (error) {
    console.error('加载缓存数据失败:', error)
  }
})
