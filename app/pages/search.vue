<script setup lang="ts">
import { SuccessFilled } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useCompanyTagSearch, useCompareUserRel, useSavedCompanies } from '~/composables/api/modules/company'
import { useSearchFlow } from '~/composables/api/modules/search'
import DICT_TYPE from '~/constants/dictType'
import emitter from '~/utils/emitter'

// 使用全局搜索状态
const useSearch = useSearchFlow()
const _t = useI18n().t // 重命名为_t，因为t在模板中仍然使用
const router = useRouter()
const route = useRoute()
const localePath = useLocalePath()
const useCache = useCacheStore()
const useCate = useCategory()
const useUser = useUserStore()
const { getDictLabel } = useCacheStore()

// 使用组合式API管理用户比对公司记录
const userCompanyCompare = useCompareUserRel()
// 使用组合式API管理用户收藏公司记录
const userSavedCompanies = useSavedCompanies()

// 存储已添加到比较列表的供应商ID
const compareList = computed(() => {
  if (userCompanyCompare.compareUserRel.value && userCompanyCompare.compareUserRel.value.companyId) {
    return userCompanyCompare.compareUserRel.value.companyId
  }
  return []
})

// const srcList = [
//   'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
//   'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg',
//   'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
//   'https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg',
//   'https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
//   'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg',
//   'https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg',
// ]
// 搜索状态控制
const hasSearchResults = ref(true) // 控制是否显示搜索结果

// 搜索筛选输入
const categoryInput = ref('')
const tagInput = ref('')
const locationInput = ref('')
const certificationInput = ref('')

// 筛选后的选项列表
const filteredLocations = ref<any[]>([])
const filteredCertifications = ref<any[]>([])
const filteredTags = ref<string[]>([])

// tag 搜索状态
const isTagSearching = ref(false)

// 示例供应商数据
const suppliers = ref<any[]>([

])

// 比较功能相关状态
const maxCompareItems = 10 // 最大比较数量

// 过滤已选择的类别，确保级联选择器不显示已选择的类别
const filteredCategoryOptions = computed(() => {
  // 深拷贝原始类别树
  const categoryTree = JSON.parse(JSON.stringify(useCache.getCategoryTree))

  // 限制层级深度的函数（只保留一级和二级品类）
  const limitDepth = (categories, currentLevel = 1, maxLevel = 2) => {
    if (!categories || !Array.isArray(categories))
      return []

    return categories.map((category) => {
      const newCategory = { ...category }

      // 如果当前层级小于最大层级且有子类别，则递归处理
      if (currentLevel < maxLevel && category.children && category.children.length > 0) {
        newCategory.children = limitDepth(category.children, currentLevel + 1, maxLevel)
      }
      else {
        // 超过最大层级或没有子类别，移除children属性
        delete newCategory.children
      }

      return newCategory
    })
  }

  // 递归过滤函数
  const filterCategories = (categories) => {
    if (!categories || !Array.isArray(categories))
      return []

    return categories.filter((category) => {
      // 如果当前类别已经被选中，则过滤掉
      if (useSearch.selectedCategories.includes(category.name))
        return false

      // 递归处理子类别
      if (category.children && category.children.length > 0) {
        category.children = filterCategories(category.children)
      }

      return true
    })
  }

  // 先限制层级深度，再过滤已选择的类别
  const limitedTree = limitDepth(categoryTree)
  return filterCategories(limitedTree)
})

// 国家/地区列表
const locations = useCache.countryListCache

// 认证列表
const certifications = useCache.getDictData(DICT_TYPE.SAAS_COMPANY_QUALIFICATIONS_TYPE_QUALITY)

// 成立年份列表
const establishYears = useCache.getDictData(DICT_TYPE.COMPANY_ESTABLISH_YEAR)

function toSupplierPage(supplier) {
  if (!useUser.isLoggedIn) {
    emitter.emit('open-login')
    return
  }
  router.push({
    path: localePath('supplier'),
    query: {
      companyId: supplier.supplier_id,
      from: 'search',
      // 保留当前搜索页面的关键query参数
      ...(route.query.keyword && { keyword: route.query.keyword }),
      ...(route.query.category && { category: route.query.category }),
    },
  })
}

function handleChange(val: any) {
  const category = val.at(-1)
  if (category) {
    useSearch.addCategory(category)
  }
  // 选择后立即清空输入框
  categoryInput.value = ''
}

// 自定义过滤方法，忽略大小写
function categoryFilterMethod(node: any, keyword: string) {
  if (!keyword)
    return true
  const text = node.text || node.label || ''
  return text.toLowerCase().includes(keyword.toLowerCase())
}

// 更新分类选中状态
function updateCategorySelection(category: string, checked: any) {
  const isChecked = Boolean(checked)
  if (isChecked) {
    useSearch.addCategory(category)
  }
  else {
    useSearch.removeCategory(category)
  }
}

// 清除所有筛选
function clearAllFilters() {
  useSearch.clearAllFilters()
}

// 跳转到联系页面
function goToContactPage() {
  // 获取对比列表中的供应商ID
  const compareSupplierIds = compareList.value

  // 通过 query 参数传递供应商ID到联系页面
  if (compareSupplierIds.length > 0) {
    router.push({
      path: localePath('contact'),
      query: {
        suppliers: compareSupplierIds.join(','),
      },
    })
  }
  else {
    router.push(localePath('contact'))
  }
}

// Add to comparison list
async function addToCompare(supplierId: number) {
  if (!useUser.isLoggedIn) {
    emitter.emit('open-login')
    return
  }
  if (!compareList.value.includes(supplierId) && compareList.value.length < maxCompareItems) {
    try {
      const result = await userCompanyCompare.addCompany(supplierId)
      if (result) {
        ElMessage.success('Added to comparison list')
      }
    }
    catch (error) {
      console.error('Failed to add to comparison list:', error)
      ElMessage.error('Failed to add to comparison list')
    }
  }
  else if (compareList.value.length >= maxCompareItems) {
    ElMessage.warning(`You can compare up to ${maxCompareItems} companies only`)
  }
}

// Remove from comparison list
async function removeFromCompare(supplierId: number) {
  try {
    const result = await userCompanyCompare.removeCompanies([supplierId])
    if (result) {
      ElMessage.success('Removed from comparison list')
    }
  }
  catch (error) {
    console.error('Failed to remove from comparison list:', error)
    ElMessage.error('Failed to remove from comparison list')
  }
}

// Clear all comparison items
async function clearAllCompare() {
  if (compareList.value.length === 0)
    return

  try {
    const result = await userCompanyCompare.removeCompanies(compareList.value)
    if (result) {
      ElMessage.success('Comparison list cleared')
    }
  }
  catch (error) {
    console.error('Failed to clear comparison list:', error)
    ElMessage.error('Failed to clear comparison list')
  }
}

// Save company
async function saveSupplier(supplierId: number) {
  try {
    // Check if the company is already saved
    if (userSavedCompanies.isCompanySaved(supplierId)) {
      // If already saved, remove from favorites
      const result = await userSavedCompanies.removeSavedCompany(supplierId)
      if (result) {
        ElMessage.success('Removed from favorites')
      }
    }
    else {
      // If not saved, add to favorites
      const result = await userSavedCompanies.saveCompany(supplierId)
      if (result) {
        ElMessage.success('Added to favorites')
      }
    }
  }
  catch (error) {
    console.error('Favorite operation failed:', error)
    ElMessage.error('Favorite operation failed')
  }
}

const isLoading = ref(false)

// 防抖搜索函数
let searchTimeout: NodeJS.Timeout | null = null
function debouncedSearch() {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    doSearch()
  }, 300) // 300ms 防抖延迟
}

// Perform search
async function doSearch(from = 1) {
  useSearch.from = from
  isLoading.value = true
  await useSearch.doSearch(useSearch.searchText || '')
  isLoading.value = false

  hasSearchResults.value = useSearch.searchResults.length > 0
  suppliers.value = useSearch.searchResults
  window.scrollTo(0, 0)
}

// Handle search event triggered from SearchHeader component
function handleSearch(value: any) {
  useSearch.searchText = value
  doSearch()
}
const hasActiveFilters = computed(() => {
  return useSearch.hasActiveFilters
})

definePageMeta({
  name: 'search',
})

// Loading state

// Load user's comparison list and favorites when component is mounted
onMounted(async () => {
  emitter.on('login-success', () => {
    loadSavedCompanies()
  })
  emitter.on('add-search-category', (val) => {
    if (!useSearch.selectedCategories.includes(val)) {
      useSearch.addCategory(val)
      doSearch()
    }
  })
  if (useUser.isLoggedIn) {
    loadSavedCompanies()
  }
})
function loadSavedCompanies() {
  if (useUser.isLoggedIn) {
    try {
      // Load comparison list and favorites in parallel
      Promise.all([
        userCompanyCompare.loadCompareUserRel(),
        userSavedCompanies.loadSavedCompanies(),
      ])
    }
    catch (error) {
      console.error('Failed to load data:', error)
      ElMessage.error('Failed to load data')
    }
    finally {
    }
  }
}
// 监听location输入框变化
watch(locationInput, (newVal) => {
  if (newVal) {
    filteredLocations.value = locations.filter((location: any) =>
      location.name.toLowerCase().includes(newVal.toLowerCase()),
    )
  }
  else {
    filteredLocations.value = locations
  }
})

// 监听certification输入框变化
watch(certificationInput, (newVal) => {
  if (newVal) {
    filteredCertifications.value = certifications.filter((cert: any) =>
      cert.label.toLowerCase().includes(newVal.toLowerCase()),
    )
  }
  else {
    filteredCertifications.value = certifications
  }
})

// tag 远程搜索函数
async function handleTagSearch(query: string) {
  if (!query || query.trim().length === 0) {
    filteredTags.value = []
    return
  }

  isTagSearching.value = true
  try {
    const response = await useCompanyTagSearch(query.trim())
    if (response.code === 0 && response.data) {
      filteredTags.value = response.data.filter(tag => !useSearch.selectedTags.includes(tag))
    }
    else {
      filteredTags.value = []
    }
  }
  catch (error) {
    console.error('Tag search failed:', error)
    filteredTags.value = []
  }
  finally {
    isTagSearching.value = false
  }
}

// 处理 tag 选择
function handleTagSelect(value: string) {
  if (value) {
    useSearch.addTag(value)
  }
  // 清空选择器的值，以便继续搜索
  tagInput.value = ''
  filteredTags.value = []
}

// 监听筛选条件变化，自动触发搜索
watch([
  () => useSearch.selectedCategories,
  () => useSearch.selectedTags,
  () => useSearch.selectedLocations,
  () => useSearch.selectedCertifications,
  () => useSearch.selectedEstablishYears,
], () => {
  // 使用防抖搜索避免过于频繁的请求
  debouncedSearch()
}, { deep: true })

// 初始化筛选列表
onMounted(() => {
  filteredLocations.value = locations
  filteredCertifications.value = certifications
  // useSearch.searchText = route.query.keyword as string || ''
  if (route.query.category) {
    useSearch.addCategory(route.query.category as string)
  }
  doSearch()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
    searchTimeout = null
  }
})
</script>

<template>
  <div>
    <SearchHeader v-model="useSearch.searchText" @search="handleSearch" />

    <div class="bg-#F5F5F5 min-h-1080px">
      <div class="p-4 px-4.16% flex max-w-full">
        <!-- 筛选区域 -->
        <div class="flex-[0_0_340px] overflow-hidden">
          <div class="filter-panel rounded-lg max-w-full w-full">
            <!-- 筛选标题 -->
            <div class="mb-6 pb-10px border-b border-gray-200 flex items-center justify-between">
              <div class="flex items-center">
                <Icon
                  name="clarity:filter-solid" size="16"
                  class="text-gray-400 mr-2"
                  :class="{ 'text-primary': hasActiveFilters }"
                />
                <span class="text-gray-700 font-medium">Filter</span>
              </div>
              <span v-if="hasActiveFilters" class="text-sm cursor-pointer hover:underline" @click="clearAllFilters">
                Clear All
              </span>
            </div>

            <!-- 分类筛选 -->
            <div class="filter-section mb-6 border-b border-gray-200">
              <h3 class="text-gray-700 mb-3">
                Categories
              </h3>
              <div class="mb-2 relative">
                <el-cascader
                  :key="useSearch.selectedCategories.length"
                  v-model="categoryInput"
                  filterable
                  class="w-full"
                  :options="filteredCategoryOptions"
                  :props="{
                    value: 'name',
                    label: 'name',
                  }"
                  placeholder="Search by categories"
                  :filter-method="categoryFilterMethod"
                  @change="handleChange"
                />
              </div>
              <div class="filter-options max-h-60 overflow-y-auto">
                <el-checkbox
                  v-for="(category, idx) in useSearch.selectedCategories"
                  :key="idx"
                  :model-value="true"
                  @change="(val) => updateCategorySelection(category, val)"
                >
                  {{ category }}
                </el-checkbox>
              </div>
            </div>

            <!-- 标签筛选 -->
            <div class="filter-section mb-6 border-b border-gray-200">
              <h3 class="text-gray-700 mb-1">
                Tags
              </h3>
              <p class="text-sm text-gray-400 mb-2">
                Need better matches? Add more tags.
              </p>
              <div class="mb-2 relative">
                <el-select
                  v-model="tagInput"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="handleTagSearch"
                  :loading="isTagSearching"
                  placeholder="Search by tags"
                  class="tag-search-select w-full"
                  @change="handleTagSelect"
                >
                  <el-option
                    v-for="tag in filteredTags"
                    :key="tag"
                    :label="tag"
                    :value="tag"
                  >
                    <span class="tag-text" :title="tag">{{ tag }}</span>
                  </el-option>
                </el-select>
              </div>

              <!-- 已选择的标签 -->
              <div v-if="useSearch.selectedTags.length > 0" class="filter-options mb-3 max-h-40 overflow-y-auto">
                <el-checkbox
                  v-for="(tag, idx) in useSearch.selectedTags"
                  :key="idx"
                  class="filter-option"
                  :model-value="true"
                  @change="(val) => !val && useSearch.removeTag(tag)"
                >
                  <span class="tag-text" :title="tag">{{ tag }}</span>
                </el-checkbox>
              </div>
            </div>

            <!-- 地区筛选 -->
            <div class="filter-section mb-6 border-b border-gray-200">
              <h3 class="text-gray-700 mb-3">
                Location
              </h3>
              <div class="mb-2 relative">
                <el-input
                  v-model="locationInput"
                  placeholder="Search by location"
                  class="filter-input"
                >
                  <template #prefix>
                    <div class="text-gray-400 flex items-center">
                      <Icon name="carbon:search" size="16" />
                    </div>
                  </template>
                </el-input>
              </div>
              <div class="filter-options max-h-60 overflow-x-hidden overflow-y-auto">
                <div v-for="location in filteredLocations" :key="location.name" class="filter-option">
                  <el-checkbox
                    :model-value="useSearch.selectedLocations.includes(location.name)"
                    @change="(val: any) => val ? useSearch.addLocation(location.name) : useSearch.removeLocation(location.name)"
                  >
                    <Icon :name="`flagpack:${location.code.toLowerCase()}`" width="32" height="24" />                                        <span class="flag-icon mr-2">{{ location.flag }}</span>
                    <span>{{ location.name }}</span>
                  </el-checkbox>
                </div>
              </div>
            </div>

            <!-- 质量认证筛选 -->
            <div class="filter-section border-b border-gray-200">
              <h3 class="text-gray-700 mb-3">
                Quality Certification
              </h3>
              <div class="mb-2 relative">
                <el-input
                  v-model="certificationInput"
                  placeholder="Input"
                  class="filter-input"
                >
                  <template #prefix>
                    <div class="text-gray-400 flex items-center">
                      <Icon name="carbon:search" size="16" />
                    </div>
                  </template>
                </el-input>
              </div>
              <div class="filter-options max-h-60 overflow-y-auto">
                <div v-for="cert in filteredCertifications" :key="cert" class="filter-option">
                  <el-checkbox
                    :model-value="useSearch.selectedCertifications.includes(cert.value)"
                    @change="(val: any) => val ? useSearch.addCertification(cert.value) : useSearch.removeCertification(cert.value)"
                  >
                    {{ cert.label }}
                  </el-checkbox>
                </div>
              </div>
            </div>

            <!-- 成立年份筛选 -->
            <div class="filter-section mt-6">
              <h3 class="text-gray-700 mb-3">
                Year Established
              </h3>
              <div class="filter-options">
                <div v-for="year in establishYears" :key="year.value" class="filter-option">
                  <el-checkbox
                    :model-value="useSearch.selectedEstablishYears.includes(year.value)"
                    @change="(val: any) => val ? useSearch.addEstablishYear(year.value) : useSearch.removeEstablishYear(year.value)"
                  >
                    {{ year.label }}
                  </el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索结果区域 -->
        <div v-loading="isLoading" class="ml-6 flex-auto" style="max-width: calc( 100% - 340px);">
          <div v-if="hasSearchResults" class="mb-4 p-4 rounded-lg min-h-810px">
            <!-- 有搜索结果时的视图 -->
            <div>
              <div v-if="useSearch.lastSearchedText" class="text-gray-700 color-#929292 mb-4">
                {{ useSearch.total }} results for {{ useSearch.lastSearchedText }}，including {{ useSearch.totalVetted }} result from ESiC Vetted
              </div>

              <!-- 搜索结果卡片列表 -->
              <div class="supplier-cards">
                <div
                  v-for="supplier in suppliers" :key="supplier.supplier_id"
                  class="supplier-card mb-10px pb-4 rounded-lg bg-white cursor-pointer shadow-sm transition-all duration-200 overflow-hidden hover:shadow-md"
                  @click="toSupplierPage(supplier)"
                >
                  <!-- 卡片头部信息 -->
                  <div class="p-6 pb-2 flex">
                    <!-- 公司logo -->
                    <div class="flex-[0_0_110px]">
                      <div class="logo-container p-2 border border-gray-200 rounded-lg flex h-28 w-28 items-center justify-center">
                        <img :src="supplier.supplier_logo" alt="Company logo" class="max-h-full max-w-full">
                        <!--                        <div v-else class="text-gray-400 bg-gray-200 flex h-full w-full items-center justify-center"> -->
                        <!--                          Logo -->
                        <!--                        </div> -->
                      </div>
                    </div>

                    <!-- 公司信息 -->
                    <div class="ml-6 flex-1">
                      <div class="flex items-start justify-between">
                        <div class="text-[#383838]">
                          <!-- 公司名和认证标签 -->
                          <div class="mb-3 flex items-center">
                            <h3 class="text-xl text-gray-800 font-bold font-medium">
                              {{ supplier.supplier_name }}
                            </h3>
                            <img v-if="supplier.esic_votted" src="~/assets/images/esic-tag.png" alt="Logo" class="logo-image ml-2">
                          </div>

                          <!-- 基础信息行 -->
                          <el-space spacer="|" class="text-sm text-[#929292] mb-3 flex flex-wrap">
                            <!-- 位置 -->
                            <div v-if="supplier.location" class="mr-3 flex items-center">
                              <span class="mr-1">Location</span>
                              <!--                              <span class="flag-icon mr-1">{{ supplier.locationFlag }}</span> -->
                              <span class="text-[#383838]">{{ supplier.location }}</span>
                            </div>

                            <!-- 成立年份 -->
                            <div v-if="supplier.established_time" class="mr-3 flex items-center">
                              <span class="mr-1">Year Established</span>
                              <span class="text-[#383838]">{{
                                dayjs(supplier.established_time).format('YYYY-MM-DD')
                              }}</span>
                            </div>

                            <!-- 年销售额 -->
                            <div v-if="supplier.annual_sales" class="mr-3 flex items-center">
                              <span class="mr-1">Annual Sales</span>
                              <span class="text-[#383838]">{{ supplier?.annual_sales ? `${supplier.annual_sales.toLocaleString()} ${supplier.annual_sales_currency}` : '' }}</span>
                            </div>

                            <!-- 员工数 -->
                            <div v-if="supplier.employee_count" class="flex items-center">
                              <span class="mr-1">No. of employee</span>
                              <span class="text-[#383838]">{{ supplier.employee_count }}</span>
                            </div>
                          </el-space>

                          <!-- 行业 -->
                          <div v-if="supplier.industry" class="text-sm text-[#929292] mb-3">
                            <span class="mr-1">Industry</span>

                            <span class="text-[#383838]">
                              <el-tag
                                v-for="item in supplier.industry"
                                :key="item"
                                class="mb-1 mr-1"
                                effect="light"
                                round
                              >
                                {{ item }}
                              </el-tag>
                              <!--                              {{ getDictLabel(DICT_TYPE.SAAS_COMPANY_INDUSTRIES_INDUSTRY_CODE, supplier.industry.split(' ')) }} -->
                            </span>
                          </div>

                          <!-- 主要客户 -->
                          <div v-if="supplier.major_clients" class="text-sm text-[#929292]">
                            <span class="mr-1">Major Clients</span>

                            <span class="text-[#383838]">
                              <el-tag
                                v-for="item in supplier.major_clients"
                                type="primary"
                                effect="light"
                                class="mb-1 mr-1"
                                round
                              >
                                {{ item }}
                              </el-tag>
                            </span>
                          </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex">
                          <el-button
                            v-if="!compareList.includes(Number(supplier.supplier_id))"
                            type="info"
                            text
                            @click.stop="addToCompare(supplier.supplier_id)"
                          >
                            Add to Compare
                          </el-button>
                          <el-button
                            v-else
                            :icon="SuccessFilled"
                            type="info"
                            text
                            @click.stop="removeFromCompare(supplier.supplier_id)"
                          >
                            Added
                          </el-button>
                          <span v-if="useUser.isLoggedIn">
                            <el-button
                              v-if="!userSavedCompanies.isCompanySaved(Number(supplier.supplier_id))"
                              plain
                              style="color: #4996B8;border-color: #4996B8"
                              @click.stop="saveSupplier(Number(supplier.supplier_id))"
                            >
                              <template #icon>
                                <Icon name="ant-design:heart-outlined" size="16" />
                              </template>
                              Save
                            </el-button>
                            <el-button
                              v-else
                              type="primary"
                              style="background-color: #4996B8;border-color: #4996B8"
                              @click.stop="saveSupplier(Number(supplier.supplier_id))"
                            >
                              <template #icon>
                                <Icon name="ant-design:heart-filled" size="16" />
                              </template>
                              Saved
                            </el-button>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 产品图片展示 -->
                  <div class="image-gallery" style="margin-left: 157px; max-width: calc(100% - 157px);" @click.stop>
                    <div class="flex whitespace-nowrap overflow-x-auto overflow-y-hidden">
                      <el-image
                        v-for="(image, index) in supplier.supplier_images"
                        :key="index"
                        style="width: 107px; height: 80px"
                        :src="image"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-src-list="supplier.supplier_images"
                        show-progress
                        :initial-index="index"
                        fit="contain"
                        class="mx-1 flex-shrink-0"
                      />
                    </div>
                  </div>
                </div>
                <div class="flex justify-center">
                  <!-- 分页组件 -->
                  <el-pagination
                    v-if="useSearch.total > 0"
                    background
                    layout="prev, pager, next"
                    :page-size="useSearch.size"
                    :current-page="useSearch.from"
                    :total="useSearch.total"
                    class="mt-4"
                    @update:current-page="doSearch"
                  />
                </div>
              </div>
            </div>
          </div>
          <div v-else class="mb-4 p-4 rounded-lg bg-white min-h-810px">
            <!-- 没有搜索结果时的视图 -->
            <div class="mt-120px flex min-h-800px justify-center">
              <div class="flex max-w-3xl w-full">
                <!-- 占位图 (左侧) -->
                <div class="mr-8 flex-shrink-0">
                  <img src="/assets/images/no-results.png" alt="No results found" class="h-128px w-128px">
                  <h2 class="text-2xl text-#4996B8 font-medium my-3">
                    Whoops!
                  </h2>
                </div>

                <!-- 提示文本区域 (右侧) -->
                <div class="flex-[0_0_645px]">
                  <div class="text-gray-700 mb-2">
                    Your search <span class="font-semibold">{{ useSearch.lastSearchedText }}</span> did not match any supplier. You may consider to:
                  </div>

                  <!-- 建议列表 -->
                  <ul class="mt-4">
                    <li class="mb-2 flex items-start">
                      <span class="text-gray-500 mr-2">•</span>
                      <span>Check the spelling</span>
                    </li>
                    <li class="mb-2 flex items-start">
                      <span class="text-gray-500 mr-2">•</span>
                      <span>Adjusting search keywords</span>
                    </li>
                    <li class="mb-2 flex items-start">
                      <span class="text-gray-500 mr-2">•</span>
                      <span>Try different keywords</span>
                    </li>
                    <li class="flex items-start">
                      <span class="text-gray-500 mr-2">•</span>
                      <div>
                        <span class="text-#4996B8 cursor-pointer hover:underline" @click="router.push(localePath('contact'))">Contact us</span>
                        <span class="ml-1">for professional sourcing and procurement outsourcing services.</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部固定比较栏 -->
    <div
      v-if="compareList.length && useUser.isLoggedIn"
      class="text-white px-4 py-2 rounded-tl-md rounded-tr-md bg-#1C3052 flex shadow-lg items-center bottom-0 right-6 fixed z-9999"
    >
      <el-button
        class="compare-btn text-white px-4 py-2 rounded-md border-none bg-#4996B8 flex w-36 items-center justify-center hover:bg-#3a7a96"
        @click="router.push(localePath('compare'))"
      >
        Compare({{ compareList.length }}/{{ maxCompareItems }})
      </el-button>
      <span class="text-sm mx-4 cursor-pointer hover:underline" @click="clearAllCompare">Clear all</span>
      <span class="text-sm pl-4 border-l border-white cursor-pointer hover:underline" @click="goToContactPage()">Contact</span>
    </div>

    <el-backtop :right="50" :bottom="80" />
  </div>
</template>

<style scoped>
.filter-section h3 {
  font-size: 16px;
  font-weight: 500;
}

.filter-input {
  width: 100%;
}

:deep(.filter-input .el-input__wrapper) {
  background-color: #fff;
  border-radius: 4px;
}

.filter-options {
  margin-top: 8px;
}

.filter-option {
  margin-bottom: 8px;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  display: flex;
  align-items: center;
  margin-right: 0;
  min-height: 24px; /* 确保复选框有足够高度 */
}

:deep(.el-checkbox__label) {
  display: flex;
  align-items: center;
  line-height: 1.4; /* 增加行高 */
  //padding: 2px 0; /* 增加上下内边距 */
}

.flag-icon {
  font-size: 16px;
}
.logo-image {
  height: 24px;
  width: 98px;
}

/* Tag 文本样式 */
.tag-text {
  display: inline-block;
  max-width: 270px; /* 减小最大宽度以适应340px容器 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  line-height: 1.4; /* 增加行高确保文字上下不被截断 */
  padding: 2px 0; /* 增加上下内边距 */
}

/* Tag 搜索选择器样式 */
.tag-search-select {
  width: 100%;
}

/* 下拉选项样式 */
:deep(.tag-search-select .el-select-dropdown .el-select-dropdown__item) {
  max-width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 10px 12px; /* 增加上下内边距 */
  line-height: 1.4; /* 增加行高 */
}

/* 限制下拉框的最大宽度 */
:deep(.tag-search-select .el-select-dropdown) {
  max-width: 300px;
  min-width: 250px;
}

/* 筛选面板样式 */
.filter-panel {
  //background-color: white;
  box-sizing: border-box;
}

/* 确保筛选区域内容不溢出 */
.filter-section {
  overflow: hidden;
}

/* 限制长文本的显示 */
.filter-section .el-checkbox__label {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 级联选择器宽度限制 */
:deep(.el-cascader) {
  width: 100%;
  max-width: 100%;
}

:deep(.el-cascader .el-input) {
  width: 100%;
}

/* 输入框宽度限制 */
:deep(.filter-input) {
  width: 100%;
  max-width: 100%;
}

/* 选择器宽度限制 */
:deep(.el-select) {
  width: 100%;
  max-width: 100%;
}

:deep(.el-tag) {
  background: #f6f6f6;
  border: none;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: top;
}

/* 确保标签内容正确显示省略号 */
:deep(.el-tag .el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.supplier-card {
  //min-width: 800px;
  max-width: 100%;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.supplier-card:hover {
  border-color: #d1d5db;
}
</style>
