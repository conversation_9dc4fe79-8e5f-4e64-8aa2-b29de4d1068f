<script setup lang="ts">
import type { CompareSupplierParams, ISupplier } from '../../types/supplier'
import { Close } from '@element-plus/icons-vue'
import { nextTick } from 'vue'
import draggable from 'vuedraggable'
import EmptyData from '~/components/EmptyData.vue'
import { useCategory, useCountryRegion } from '~/composables'
import { useCompareSupplier } from '~/composables/api/modules'
import { useCompareUserRel } from '~/composables/api/modules/company'
import DICT_TYPE from '~/constants/dictType'

const companyPhotoTypes = ref(['COMPANY_PHOTO/FILE'])

interface Capability {
  id: number
  companyId: number
  language: string
  categoryFirst: string | number
  categorySecond: string | number
  processCapability: string
  productionCap: string
  aiReport: string
  extIds: number[]
  createTime: string
}

function filteredContacts(supplier) {
  return supplier.companyInfo.contactList.slice(1).filter(contact => contact.mail || contact.phone)
}

// 将生产能力数据按类别分组
function groupedProductionCapacity(productionCapacity: any) {
  if (!productionCapacity) {
    return {}
  }

  const grouped: Record<string, string[]> = {}

  // 处理新的JSON格式
  if (typeof productionCapacity === 'object') {
    // 遍历顶级对象（如 "Capabilities Overview"）
    Object.keys(productionCapacity).forEach((topLevelKey) => {
      const topLevelData = productionCapacity[topLevelKey]

      if (typeof topLevelData === 'object' && topLevelData !== null) {
        // 遍历二级对象
        Object.keys(topLevelData).forEach((key) => {
          const value = topLevelData[key]

          if (value) {
            if (!grouped[key]) {
              grouped[key] = []
            }

            // 处理数组值
            if (Array.isArray(value)) {
              grouped[key].push(...value)
            }
            // 处理对象值
            else if (typeof value === 'object') {
              Object.keys(value).forEach((subKey) => {
                const subValue = value[subKey]
                if (Array.isArray(subValue)) {
                  grouped[subKey] = grouped[subKey] || []
                  grouped[subKey].push(...subValue)
                }
                else if (subValue) {
                  grouped[subKey] = grouped[subKey] || []
                  grouped[subKey].push(subValue)
                }
              })
            }
            // 处理字符串值
            else {
              grouped[key].push(value)
            }
          }
        })
      }
    })
  }

  return grouped
}

definePageMeta(
  {
    layout: 'home',
    requiresAuth: true,
  },
)
const { getCountryRegionNameById, getCountryRegionCodeById } = useCountryRegion()
const { getCategoryNameById } = useCategory()

// 初始化供应商数据
const suppliers = ref<ISupplier[]>([])
const router = useRouter()
const localePath = useLocalePath()
const { getDictLabel, getDictExtendValue } = useCacheStore()

const drag = ref(false)
const compareContainer = ref(null)
const showHeader = ref(true)
const loading = ref(true)

function handleClick(e: MouseEvent) {
  e.preventDefault()
}

function handleScroll() {
  if (!compareContainer.value)
    return

  const scrollTop = window.scrollY || document.documentElement.scrollTop
  // 当滚动超过120px时，隐藏顶部元素
  showHeader.value = scrollTop <= 100
}

const userCompanyCompare = useCompareUserRel()

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  getCompareData()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

const compareList = computed(() => {
  if (userCompanyCompare.compareUserRel.value && userCompanyCompare.compareUserRel.value.companyId) {
    return userCompanyCompare.compareUserRel.value.companyId
  }
  return []
})

// 计算 Major Clients 部分的动态高度
const majorClientsHeight = computed(() => {
  if (suppliers.value.length === 0)
    return 'h-30'

  let maxLines = 1 // 至少一行

  suppliers.value.forEach((supplier) => {
    if (supplier.businessInfo.majorClientList && supplier.businessInfo.majorClientList.length > 0) {
      const clientsText = supplier.businessInfo.majorClientList.join('、')
      // 估算文本行数，假设每行大约能容纳40个字符（包括中文字符）
      const estimatedLines = Math.ceil(clientsText.length / 40)
      maxLines = Math.max(maxLines, estimatedLines)
    }
  })

  // 根据行数返回对应的高度类
  if (maxLines <= 1)
    return 'h-12'
  if (maxLines <= 2)
    return 'h-18'
  if (maxLines <= 3)
    return 'h-24'
  if (maxLines <= 4)
    return 'h-30'
  return 'h-36' // 最大高度
})

// 计算 Industry 部分的动态高度
const industryHeight = computed(() => {
  if (suppliers.value.length === 0)
    return 'h-12'

  let maxLines = 1 // 至少一行

  suppliers.value.forEach((supplier) => {
    if (supplier.businessInfo.industryList && supplier.businessInfo.industryList.length > 0) {
      const industryText = getDictLabel(DICT_TYPE.SAAS_COMPANY_INDUSTRIES_INDUSTRY_CODE, supplier.businessInfo.industryList)
      // 估算文本行数，假设每行大约能容纳40个字符（包括中文字符）
      const estimatedLines = Math.ceil(industryText.length / 40)
      maxLines = Math.max(maxLines, estimatedLines)
    }
  })

  // 根据行数返回对应的高度类
  if (maxLines <= 1)
    return 'h-12'
  if (maxLines <= 2)
    return 'h-18'
  if (maxLines <= 3)
    return 'h-24'
  if (maxLines <= 4)
    return 'h-30'
  return 'h-36' // 最大高度
})

// 计算 Registered Address 部分的动态高度
const registeredAddressHeight = computed(() => {
  if (suppliers.value.length === 0)
    return 'h-12'

  let maxLines = 1 // 至少一行

  suppliers.value.forEach((supplier) => {
    if (supplier.businessInfo.registeredAddress) {
      const addressText = supplier.businessInfo.registeredAddress
      // 估算文本行数，假设每行大约能容纳40个字符（包括中文字符）
      const estimatedLines = Math.ceil(addressText.length / 40)
      maxLines = Math.max(maxLines, estimatedLines)
    }
  })

  // 根据行数返回对应的高度类
  if (maxLines <= 1)
    return 'h-12'
  if (maxLines <= 2)
    return 'h-18'
  if (maxLines <= 3)
    return 'h-24'
  if (maxLines <= 4)
    return 'h-30'
  return 'h-36' // 最大高度
})

async function getCompareData() {
  loading.value = true
  try {
    await userCompanyCompare.loadCompareUserRel()
    // 修复参数结构，符合CompareSupplierParams类型
    if (compareList.value.length === 0) {
      loading.value = false
      return
    }
    const _params: CompareSupplierParams = {
      companyIds: compareList.value.join(','),
    }
    const response = await useCompareSupplier(_params)
    response.data.forEach((element) => {
      element.selectedCapability = 0
      element.showMoreContact = false
      // 处理每个供应商的能力数据
      element.capabilityVOS.forEach((capability) => {
        // 如果 productionCap 是字符串，则解析为 JSON
        if (typeof capability.productionCap === 'string') {
          try {
            capability.productionCap = JSON.parse(capability.productionCap)
          }
          catch (error) {
            console.error('Failed to parse productionCap:', error)
          }
        }
      })
    })
    if (response && response.code === 0 && response.data) {
      suppliers.value = response.data
    }
  }
  finally {
    loading.value = false
  }
}
function handleAiReport(capability: Capability, supplier) {
  if (!supplier.capabilityVOS[supplier.selectedCapability].aiReport) {
    ElMessage.error('This Supplier Do not Generate Ai Report,Pls contact Admin!!!')
    return
  }
  router.push({
    path: localePath(`/report/${supplier.businessInfo.id}`),
    query: {
      cateId: capability.categoryFirst,
      from: 'compare',
    },
  })
}

function removeSupplier(id) {
  suppliers.value = suppliers.value.filter(supplier => supplier.companyInfo.companyId !== id)
  userCompanyCompare.removeCompanies([id])
}

// 添加响应式变量来控制图片预览
const previewVisible = ref(false)
const previewImages = ref<string[]>([])
const previewIndex = ref(0)

function openMoreImages(albumList: any[], startIndex: number) {
  // 设置预览图片列表和起始索引
  previewImages.value = albumList.map(item => item.url)
  previewIndex.value = startIndex
  previewVisible.value = true

  // 使用 nextTick 确保 DOM 更新后再触发预览
  nextTick(() => {
    // 查找隐藏的图片元素并触发点击
    const hiddenImage = document.querySelector('.hidden-preview-trigger img') as HTMLImageElement
    if (hiddenImage) {
      hiddenImage.click()
    }
  })
}

// 获取供应商logo图片
function getSupplierLogo(supplier: any) {
  if (supplier.albumInfoList && supplier.albumInfoList.length > 0) {
    const logoImage = supplier.albumInfoList.find((item: any) => item.businessType === 'COMPANY_LOGO/FILE')
    if (logoImage && logoImage.url) {
      return logoImage.url
    }
  }
  // 如果没有找到logo，返回默认logo
  // return '/assets/images/logo.png'
}
</script>

<template>
  <div ref="compareContainer" class="bg-#F5F7FA">
    <NavBar v-show="showHeader" style="background: #DAE8F0" class="w-full transition-all duration-300 pos-fixed z-30" />
    <div class="mt-71px px-80px pb-10px bg-white w-full transition-all duration-300 z-30" :class="{ 'pos-fixed': showHeader }">
      <div v-show="showHeader" class="font-size-28px pt-30px" :class="{ 'opacity-0 pointer-events-none': !showHeader, 'opacity-100': showHeader }">
        Compare suppliers ({{ suppliers.length }}/10)
      </div>
      <div v-show="showHeader" class="color-#565656 mb-4 mt-2" :class="{ 'opacity-0 pointer-events-none': !showHeader, 'opacity-100': showHeader }">
        Need professional outsourcing Services? Try
        <el-button link type="info" @click="router.push(localePath('contact'))">
          Contact ESiC
        </el-button>
        with suppliers list below
      </div>
      <el-affix v-if="suppliers.length > 0" position="top">
        <div class="mx--110px px-110px pb-2 border-b border-gray:30 bg-white">
          <div class="pl-50px bg-white flex items-center">
            <div class="mb-2 mr-6">
              Reorder
            </div>
            <draggable
              v-model="suppliers"
              item-key="id"
              :animation="150"
              ghost-class="ghost"
              class="scrollbar-hide py-2 flex gap-0 items-center overflow-x-auto"
              @start="drag = true"
              @end="drag = false"
            >
              <template #item="{ element }">
                <div class="p-1.65 border border-#E5E5E5 bg-white flex max-w-150px cursor-move items-center justify-between">
                  <div class="flex min-w-0 w-full items-center">
                    <div class="mr-2 flex flex-shrink-0 items-center">
                      <Icon name="mdi:drag-vertical" size="20" />
                    </div>
                    <div class="font-size-12px flex-grow min-w-0 truncate">
                      {{ element.companyInfo.name }}
                    </div>
                  </div>
                  <div class="ml-1 flex-shrink-0">
                    <el-button size="small" text circle class="flex items-center justify-center" @click="removeSupplier(element.companyInfo.companyId)">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </el-affix>
    </div>

    <div>
      <!-- Loading状态显示 -->
      <div v-if="loading" class="bg-white flex min-h-screen items-center justify-center">
        <div class="text-center">
          <div v-loading="true" element-loading-text="Loading suppliers..." class="h-200px w-200px" />
        </div>
      </div>

      <!-- 空状态显示 -->
      <div v-else-if="suppliers.length === 0" class="bg-white flex min-h-screen items-center justify-center">
        <div class="text-center">
          <!-- 自定义空状态图标 -->
          <div class="mb-6 flex justify-center">
            <img src="/assets/images/compare-no-result.png" class="h-200px w-200px" alt="">
          </div>

          <h3 class="text-xl text-gray-800 font-medium mb-4">
            No supplier here
          </h3>
          <p class="text-gray-600 mb-6">
            You may consider to add supplier by searching
          </p>

          <el-button
            type="primary"
            plain
            size="large"
            @click="router.push(localePath('search'))"
          >
            Search for suppliers
          </el-button>
        </div>
      </div>

      <!-- 有供应商数据时的正常显示 -->
      <div v-else class="flex">
        <div class="bg-#F8F8F8 flex-[0_0_170px] h-100% w-170px left-0 top-0 pos-fixed z-29">
          <el-anchor
            direction="vertical"
            type="default"
            :offset="368"
            class="custom-anchor z-60"
            @click="handleClick"
          >
            <el-anchor-link href="#part1" title="Company Info" class="custom-anchor-link" />
            <el-anchor-link href="#part2" title="Business Info" class="custom-anchor-link" />
            <el-anchor-link href="#part3" title="Categories & Manufacturing Capabilities" class="custom-anchor-link" />
            <el-anchor-link href="#part4" title="Quality Certification" class="custom-anchor-link" />
            <el-anchor-link href="#part5" title="Management System" class="custom-anchor-link" />
            <el-anchor-link href="#part6" title="Company Album" class="custom-anchor-link" />
          </el-anchor>
        </div>
        <div
          class="scrollbar-hide ml-170px w-full overflow-visible"
          :class="{ 'mt-260px': showHeader }"
        >
          <div class="bg-white flex" style="min-width: calc( 100vw - 353px);">
            <!-- 遍历供应商数据 -->
            <div v-for="(supplier, index) in suppliers" :key="supplier.companyInfo.companyId" class="mx-2 px-2 flex-shrink-0 w-520px">
              <div>
                <div
                  class="rounded-lg bg-white"
                  style="
                position:sticky;
                top: 62px;
                z-index: 20;
              "
                >
                  <div class="p-4 pb-0">
                    <div class="flex items-center justify-between">
                      <div class="flex flex-1 min-w-0 items-center">
                        <div class="logo-container mr-2 p-1 border border-gray-200 rounded-lg flex h-10 w-10 items-center justify-center">
                          <img
                            :src="getSupplierLogo(supplier)"
                            alt="公司logo"
                            class="rounded flex-shrink-0 max-h-full max-w-full"
                          >
                        </div>

                        <h3 class="text-base font-medium flex-1 min-w-0 whitespace-nowrap text-ellipsis overflow-hidden">
                          {{ supplier.companyInfo.name }}
                        </h3>
                        <img
                          v-if="supplier.companyInfo.esicSource"
                          src="~/assets/images/esic-tag.png" alt="Logo"
                          class="ml-2 flex-shrink-0 h-24px w-98px"
                        >
                      </div>
                      <div class="ml-2 flex-shrink-0">
                        <el-icon class="close-icon cursor-pointer" @click="removeSupplier(supplier.companyInfo.companyId)">
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="supplier.companyInfo.contactList && supplier.companyInfo.contactList.length > 0" class="mb-4 mt-4 px-5 pt-3 pt-4 border border-gray:30 border-rd-xl">
                  <div>
                    <div class="mb-2 flex items-center overflow-hidden">
                      <span class="text-gray-700 truncate">{{ supplier.companyInfo.contactList[0].name }}</span>
                      <span class="mx-2 flex-shrink-0">|</span>
                      <span class="text-gray-500 truncate">{{ supplier.companyInfo.contactList[0].title }}</span>
                    </div>
                    <div class="text-sm mb-2 flex items-center">
                      <Icon name="material-symbols:mail-outline" size="20" class="text-gray-400 mr-1" />
                      <CopyButton>{{ supplier.companyInfo.contactList[0].mail }}</CopyButton>
                    </div>
                    <div class="text-sm flex items-center">
                      <Icon name="line-md:phone" size="20" class="text-gray-400 mr-1" />
                      <CopyButton>{{ supplier.companyInfo.contactList[0].phone }}</CopyButton>
                    </div>
                  </div>
                  <div class="mt-3 py-1 text-center border-t border-gray:30">
                    <el-popover
                      placement="bottom"
                      :width="402"
                      trigger="click"
                      popper-class="contact-popover"
                    >
                      <template #reference>
                        <el-button :disabled="filteredContacts(supplier).length === 0" type="info" link @click="supplier.showMoreContact = !supplier.showMoreContact">
                          More
                        </el-button>
                      </template>
                      <div class="contact-list-container">
                        <!--                        <div class="pb-1 border-b border-gray:30 flex justify-center"> -->
                        <!--                          <el-button type="info" link @click="supplier.showMoreContact = !supplier.showMoreContact"> -->
                        <!--                            Show less -->
                        <!--                          </el-button> -->
                        <!--                        </div> -->
                        <div v-for="(contact, contactIndex) in filteredContacts(supplier)" :key="contactIndex" class="contact-item">
                          <div class="mb-2 flex items-center overflow-hidden">
                            <span class="text-gray-700 truncate">{{ contact.name }}</span>
                            <span class="mx-2 flex-shrink-0">|</span>
                            <span class="text-gray-500 truncate">{{ contact.title }}</span>
                          </div>
                          <div class="text-sm mb-2 flex items-center">
                            <Icon name="material-symbols:mail-outline" size="20" class="text-gray-400 mr-1" />
                            <CopyButton>{{ contact.mail }} </CopyButton>
                          </div>
                          <div class="text-sm flex items-center">
                            <Icon name="line-md:phone" size="20" class="text-gray-400 mr-1" />
                            <CopyButton>{{ contact.phone }} </CopyButton>
                          </div>
                          <div v-if="contactIndex < supplier.companyInfo.contactList.length - 2" class="my-2 border-t border-gray:30" />
                        </div>
                      </div>
                    </el-popover>
                  </div>
                </div>

                <div
                  id="part1"
                >
                  <div class="mt-3 pt-3">
                    <div class="gap-3 grid grid-cols-1">
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Location
                        </div>
                        <div v-if="supplier.businessInfo?.country" class="flex items-center">
                          <Icon :name="`flagpack:${getCountryRegionCodeById(supplier.businessInfo?.country).toLowerCase()}`" width="32" height="24" />
                          <span class="ml-2">
                            {{ getCountryRegionNameById(supplier.businessInfo?.country) }}
                          </span>
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Year Established
                        </div>
                        <div>{{ new Date(supplier.businessInfo.establishAt).toLocaleDateString() }}</div>
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Annual Sales
                        </div>
                        <div v-if="supplier.businessInfo.annualSales">
                          {{ supplier.businessInfo.annualSales.toLocaleString() }} {{ supplier.businessInfo.annualSalesCurrency }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          No. of employee
                        </div>
                        <div v-if="supplier.businessInfo.allStaff">
                          {{ supplier.businessInfo.allStaff }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30" :class="industryHeight">
                        <div class="text-sm text-gray-500">
                          Industry
                        </div>

                        <div> {{ getDictLabel(DICT_TYPE.SAAS_COMPANY_INDUSTRIES_INDUSTRY_CODE, supplier.businessInfo.industryList)?.split(',').join('、') }}</div>
                      </div>
                      <div class="pb-2 border-b border-gray:30" :class="majorClientsHeight">
                        <div class="text-sm text-gray-500">
                          Major Clients
                        </div>
                        <div v-if="supplier.businessInfo.majorClientList.length">
                          {{ supplier.businessInfo.majorClientList?.join('、') || '-' }}
                        </div>
                        <EmptyData v-else />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  id="part2"
                  class="mt-4"
                >
                  <div>
                    <div class="gap-3 grid grid-cols-1">
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Legal Representative
                        </div>
                        <div v-if=" supplier.businessInfo.legalPerson">
                          {{ supplier.businessInfo.legalPerson || '-' }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Registered Capital
                        </div>
                        <div>{{ supplier.businessInfo.registerCapital.toLocaleString() }} {{ supplier.businessInfo.registerCapitalCurrency }}</div>
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Operating Status
                        </div>
                        <div v-if="supplier.businessInfo.operateStatus">
                          {{ supplier.businessInfo.operateStatus || '-' }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          VAT No.
                        </div>
                        <div v-if="supplier.businessInfo.vatNumber">
                          {{ supplier.businessInfo.vatNumber || '-' }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Business Type
                        </div>
                        <div v-if="supplier.businessInfo.businessType">
                          {{ getDictLabel(DICT_TYPE.SAAS_COMPANY_BUSINESS_TYPES_BUSINESS_TYPE, supplier.businessInfo.businessType) }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30" :class="registeredAddressHeight">
                        <div class="text-sm text-gray-500">
                          Registered Address
                        </div>
                        <div v-if="supplier.businessInfo.registeredAddress">
                          {{ supplier.businessInfo.registeredAddress || '-' }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Site Area
                        </div>
                        <div v-if="supplier.businessInfo.siteArea">
                          {{ supplier.businessInfo.siteArea }}
                          {{ getDictLabel(DICT_TYPE.SAAS_UNIT_AREA, supplier.businessInfo.siteAreaUnit) }}
                        </div>
                        <EmptyData v-else />
                      </div>
                      <div class="pb-2 border-b border-gray:30">
                        <div class="text-sm text-gray-500">
                          Floor Space
                        </div>
                        <div v-if="supplier.businessInfo.totalFloorAreaSqm">
                          {{ supplier.businessInfo.totalFloorAreaSqm }} {{ supplier.businessInfo.totalFloorAreaSqmUnit }}
                        </div>
                        <EmptyData v-else />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  id="part3"
                  class="mt-4 bg-white"
                >
                  <div v-if="supplier.capabilityVOS.length" class="mb-6">
                    <div class="mb-4 flex items-center justify-between">
                      <el-select
                        v-model="supplier.selectedCapability"
                        class="w-full"
                      >
                        <el-option
                          v-for="(capability, index) in supplier.capabilityVOS"
                          :key="capability.id"
                          :value="index"
                          :label="`${getCategoryNameById(capability.categoryFirst)}/${getCategoryNameById(capability.categorySecond)}`"
                        />
                      </el-select>
                    </div>
                    <div class="px-3 py-1 bg-#F8F8F8 h-850px overflow-y-auto">
                      <div class="mt-2 border-b border-gray:30">
                        <div class="mb-2 flex items-center justify-between">
                          <h4 class="font-bold">
                            Process Capability
                          </h4>

                          <el-button
                            type="info"
                            class="mr-4"
                            plain @click="handleAiReport(supplier.capabilityVOS[supplier.selectedCapability], supplier)"
                          >
                            ESiC Report
                          </el-button>
                        </div>
                        <p>
                          {{ supplier.capabilityVOS[supplier.selectedCapability].extIds?.map(item => getCategoryNameById(item)).filter(Boolean).join('、 ') }}
                        </p>
                        <div class="p-4">
                          <p class="text-sm text-gray-600 leading-relaxed">
                            {{ supplier.capabilityVOS[supplier.selectedCapability].processCapability }}
                          </p>
                        </div>
                      </div>

                      <div v-if="supplier.capabilityVOS[supplier.selectedCapability].productionCap" class="mt-5">
                        <h4 class="font-bold mb-3 flex items-center">
                          Production Capacity
                        </h4>
                        <!-- 按类别分组显示生产能力 -->
                        <div class="space-y-3">
                          <div
                            v-for="(group, categoryKey) in groupedProductionCapacity(supplier.capabilityVOS[supplier.selectedCapability].productionCap)" :key="categoryKey"
                            class="mb-3 pb-3 border-b border-gray:30 last:border-b-0"
                          >
                            <div class="text-sm text-gray-600 font-medium mb-1">
                              {{ categoryKey }}
                            </div>
                            <div class="text-sm text-gray-800 leading-relaxed">
                              {{ group.join('、') }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="mb-6 px-3 border-b border-gray:30 overflow-y-auto">
                    <el-select
                      class="mb-4 w-full"
                      placeholder="Not available"
                      disabled
                    >
                      <el-option
                        v-for="(capability, index) in supplier.capabilityVOS"
                        :key="capability.id"
                        :value="index"
                        :label="`${getCategoryNameById(capability.categoryFirst)}/${getCategoryNameById(capability.categorySecond)}`"
                      />
                    </el-select>
                    <div
                      class="color-gray-400 py-1 pt-10"
                      :style="{ height: suppliers.every(supplier => supplier.capabilityVOS.length === 0) ? '250px' : '850px' }"
                    >
                      <EmptyData type="custom" custom-class="color-gray-400" />
                    </div>
                  </div>
                </div>
                <div
                  v-if="supplier.qualityCetiList && supplier.qualityCetiList.length > 0"
                  id="part4"
                  class="mt-4 bg-white h-366px overflow-y-auto"
                >
                  <div class="mb-6">
                    <h4 class="font-medium mb-4">
                      Quality Certification
                    </h4>
                    <div class="gap-4 grid grid-cols-2">
                      <div v-for="cert in supplier.qualityCetiList" :key="cert.id" class="p-4 rounded-lg bg-gray-100">
                        <div class="mb-2 flex items-center justify-center">
                          <el-image
                            :src="getDictExtendValue(DICT_TYPE.SAAS_COMPANY_QUALIFICATIONS_TYPE_QUALITY, cert.qualityType)"
                            class="h-20 w-20"
                          />
                          <!--                  <div class="rounded bg-gray-300 flex h-16 w-16 items-center justify-center"> -->
                          <!--                    {{ cert.qualityType }} -->
                          <!--                  </div> -->
                        </div>
                        <div v-if="cert.validEndTime" class="text-xs text-gray-500 flex justify-center">
                          Validity Period {{ cert.validEndTime ? new Date(cert.validEndTime).toISOString().split('T')[0] : 'N/A' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div
                    class="mt-4 border-gray:30 bg-white h-366px overflow-y-auto"
                  >
                    <div class="px-2 py-1">
                      <div class="p-4">
                        <EmptyData type="block" />
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  v-if="supplier.managementSysList && supplier.managementSysList.length > 0"
                  id="part5"
                  class="mt-4 bg-white h-236px overflow-y-auto"
                >
                  <div class="mb-6 border-t border-gray:30">
                    <div class="px-2 py-1">
                      <div class="p-4">
                        <ul class="pl-5 list-disc space-y-4">
                          <li v-for="system in supplier.managementSysList" :key="system.id" class="text-sm">
                            <span class="font-medium">{{ system.name }}{{ system.brand ? `/${system.brand}` : '' }}</span>
                            <p v-if="system.description">
                              {{ system.description }}
                            </p>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div
                    class="mt-4 border-t border-gray:30 bg-white h-236px overflow-y-auto"
                  >
                    <div class="px-2 py-1">
                      <div class="p-4">
                        <EmptyData type="block" />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="supplier.albumInfoList && supplier.albumInfoList.length > 0"
                  id="part6"
                  class="mt-4 bg-white"
                >
                  <div class="mb-6 pt-4 border-t border-gray:30">
                    <div class="px-2 py-1">
                      <div class="gap-4 grid grid-cols-3">
                        <template v-for="(album, albumIndex) in supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId)" :key="album.id">
                          <!-- 显示前8张图片 -->
                          <el-image
                            v-if="albumIndex < 8"
                            style="width: 100%; height: 120px"
                            :src="album.url"
                            :zoom-rate="1.2"
                            :max-scale="7"
                            :min-scale="0.2"
                            :preview-src-list="supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId).map(item => item.url)"
                            show-progress
                            :initial-index="albumIndex"
                            fit="contain"
                            class="border border-gray-200 rounded-md"
                          />
                          <!-- More按钮 - 当图片数量大于9张且当前是第8张时显示 -->
                          <div
                            v-else-if="albumIndex === 8 && supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId).length > 9"
                            class="border border-gray-200 rounded-md bg-gray-50 flex cursor-pointer transition-colors items-center justify-center hover:bg-gray-100"
                            style="width: 100%; height: 120px"
                            @click="openMoreImages(supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId), 8)"
                          >
                            <div class="text-center">
                              <Icon name="carbon:add" class="text-2xl text-gray-500 mb-1" />
                              <div class="text-sm text-gray-500">
                                More
                              </div>
                              <!--                              <div class="text-xs text-gray-400"> -->
                              <!--                                +{{ supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType)).length - 8 }} -->
                              <!--                              </div> -->
                            </div>
                          </div>
                          <!-- 当图片数量为9张且当前是第8张时，显示第9张图片 -->
                          <el-image
                            v-else-if="albumIndex === 8 && supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId).length === 9"
                            style="width: 100%; height: 120px"
                            :src="album.url"
                            :zoom-rate="1.2"
                            :max-scale="7"
                            :min-scale="0.2"
                            :preview-src-list="supplier.albumInfoList.filter(i => companyPhotoTypes.includes(i.businessType)).map(item => item.url)"
                            show-progress
                            :initial-index="8"
                            fit="contain"
                            class="border border-gray-200 rounded-md"
                          />
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div
                    class="mt-4 border-t border-gray:30 bg-white h-236px overflow-y-auto"
                  >
                    <div class="px-2 py-1">
                      <div class="p-4">
                        <EmptyData type="block" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-backtop :right="50" :bottom="80" />

    <!-- 隐藏的图片预览触发器 -->
    <div v-if="previewVisible" class="hidden-preview-trigger" style="position: fixed; top: -9999px; left: -9999px;">
      <el-image
        :src="previewImages[previewIndex]"
        :preview-src-list="previewImages"
        :initial-index="previewIndex"
        preview-teleported
        @close="previewVisible = false"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.close-btn-container {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    right: -28px;
    top: 50%;
    transform: translateY(-50%);
    height: 30px;
    width: 1px;
    background-color: #e0e0e0;
  }
}

/* 自定义滚动条样式 */
.custom-scrollbar-track {
  position: relative;
  height: 12px;
  background-color: #fff;
  border-radius: 3px;
  cursor: pointer;
}

.custom-scrollbar-thumb {
  position: absolute;
  height: 100%;
  background-color: #f8f8f8;
  border: 1px solid #eaeaea;
  border-radius: 3px;
  cursor: grab;
  top: 0;
  left: 0;
}

.custom-scrollbar-thumb:hover {
  background-color: #eef0f1;
}

.custom-scrollbar-thumb:active {
  cursor: grabbing;
}
</style>

<!-- 使用全局样式解决affix包裹的问题 -->

<style lang="scss">
/* 自定义锚点样式 - 不使用scoped以确保能穿透affix组件 */

.custom-anchor {
  position: fixed;
  top: 260px;
  background: #ffffff;
  font-size: 16px !important;
  width: 170px;
  color: #565656;
  .el-anchor__marker {
    display: none;
  }
  .el-anchor {
    border-right: none !important;
  }

  .el-anchor-ink {
    display: none !important;
  }

  .el-anchor__link {
    text-align: right;
    word-break: break-all;
    padding: 20px 10px !important;
    border-left: none !important;
    white-space: wrap !important;
    text-overflow: initial;
    &.is-active {
      //background-color: #c4c4c4 !important;
      font-weight: bold;

      &::before {
        display: none !important;
      }

      .el-anchor-link__title {
        color: #333 !important;
        font-weight: 500 !important;
      }
    }

    .el-anchor-link__title {
      color: #565656;
      font-size: 14px;
      transition: all 0.3s;
      text-align: right;
      padding-right: 16px;
      line-height: 1.5;
    }
  }
}

// 联系人弹出层样式
.contact-popover {
  padding: 16px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 8px !important;
}

.contact-list-container {
  max-height: 400px;
  overflow-y: auto;
}

.contact-item {
  padding: 8px 0;
}

.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.3s;
}
</style>
