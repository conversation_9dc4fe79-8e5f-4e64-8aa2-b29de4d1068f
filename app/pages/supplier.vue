<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCompanyInfo, useSavedCompanies } from '~/composables/api/modules/company'
import { useSearchFlow } from '~/composables/api/modules/search'
import { useCategory, useCountryRegion } from '~/composables/useCache'
import { useCopyToClipboard } from '~/composables/useCopyToClipboard'
import DICT_TYPE from '~/constants/dictType'

// 使用兼容性更好的复制功能
const { copy: copyToClipboard } = useCopyToClipboard()

// 定义能力信息类型
interface Capability {
  id: number
  companyId: number
  language: string
  categoryFirst: string | number
  categorySecond: string | number
  processCapability: string
  productionCap: string
  aiReport: string
  extIds: number[]
  createTime: string
}

// 定义品类项类型
interface CategoryItem {
  id: string | number
  name: string
}

const router = useRouter()
const route = useRoute()
const localePath = useLocalePath()
const { getDictLabel, getDictExtendValue } = useCacheStore()
const { getCountryRegionNameById, getCountryRegionCodeById } = useCountryRegion()

// 面包屑相关逻辑
const breadcrumbItems = computed(() => {
  const items = [
    { label: 'Home', to: { path: localePath('/') } },
  ]

  // 根据来源页面添加中间路径
  // 优先使用路由查询参数，其次使用referrer（仅在客户端）
  let fromSearch = route.query.from === 'search'
  let fromCollection = route.query.from === 'collection'
  let fromCompare = route.query.from === 'compare'

  // 在客户端检查referrer作为备选方案
  if (import.meta.client && !fromSearch && !fromCollection && !fromCompare) {
    const referrer = document.referrer
    fromSearch = referrer.includes('/search')
    fromCollection = referrer.includes('/collection')
    fromCompare = referrer.includes('/compare')
  }

  // 构建带有query参数的路由对象
  if (fromSearch) {
    // 保留搜索页面的query参数
    const searchQuery = { ...route.query }
    delete searchQuery.from // 移除from参数，避免循环
    delete searchQuery.companyId // 移除supplier特有的参数
    items.push({
      label: 'Search',
      to: {
        path: localePath('/search'),
        query: Object.keys(searchQuery).length > 0 ? searchQuery : undefined,
      },
    })
  }
  else if (fromCollection) {
    items.push({ label: 'Saved Suppliers', to: { path: localePath('/collection') } })
  }
  else if (fromCompare) {
    items.push({ label: 'Compare', to: { path: localePath('/compare') } })
  }
  else {
    // 默认显示Search
    items.push({ label: 'Search', to: { path: localePath('/search') } })
  }

  // 添加当前公司名称
  if (companyInfo.value?.name) {
    items.push({ label: companyInfo.value.name, to: null })
  }

  return items
})

// 返回上一页功能
function goBack() {
  if (window.history.length > 1) {
    router.back()
  }
  else {
    router.push(localePath('/'))
  }
}
// 获取公司信息API
const {
  companyInfo,
  businessInfo,
  capabilities,
  qualityCerts,
  managementSystems,
  albumInfos,
  loading,
  error,
  fetchCompanyDetail,
} = useCompanyInfo()
const companyPhotoTypes = ['COMPANY_PHOTO/FILE']
const companyLogoTypes = ['COMPANY_LOGO/FILE']

// 获取品类API
const { getCategoryNameById } = useCategory()

// 预处理公司相册URL列表
const companyAlbumUrls = computed(() => {
  return albumInfos.value.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId).map((item: { url: string }) => item.url)
})

// 预处理Logo相册URL列表
const logoAlbumUrls = computed(() => {
  return albumInfos.value.filter(i => companyLogoTypes.includes(i.businessType) && i.fileId).map((item: { url: string }) => item.url)
})

// 从能力信息中提取一级品类
const firstCategories = computed<CategoryItem[]>(() => {
  // 获取所有不重复的一级品类ID
  const uniqueCategoryIds = Array.from(new Set(
    capabilities.value.map((cap: Capability) => cap.categoryFirst),
  ))

  // 转换为名称
  return uniqueCategoryIds.map(id => ({
    id,
    name: getCategoryNameById(id),
  }))
})

// 当前选中的一级品类
const activeCategory = ref('')

// 根据选中的一级品类获取对应的二级品类
const subCategories = computed<CategoryItem[]>(() => {
  if (!activeCategory.value) {
    return []
  }

  // 找到所有匹配当前一级品类的能力项
  const matchedCapabilities = capabilities.value.filter(
    (cap: Capability) => getCategoryNameById(cap.categoryFirst) === activeCategory.value,
  )

  // 获取所有不重复的二级品类ID
  const uniqueSubCategoryIds = Array.from(new Set(
    matchedCapabilities.map((cap: Capability) => cap.categorySecond),
  ))

  // 转换为名称
  return uniqueSubCategoryIds.map(id => ({
    id,
    name: getCategoryNameById(id),
  }))
})

// 当前选中的二级品类
const activeSubCategory = ref('')

// 根据选中的一级和二级品类获取对应的能力信息
const filteredCapabilities = computed(() => {
  if (!activeCategory.value) {
    return capabilities.value
  }

  return capabilities.value.filter((cap: Capability) => {
    const firstCategoryMatches = getCategoryNameById(cap.categoryFirst) === activeCategory.value

    if (!activeSubCategory.value) {
      return firstCategoryMatches
    }

    return firstCategoryMatches && getCategoryNameById(cap.categorySecond) === activeSubCategory.value
  })
})

// 过滤出有邮箱或电话的可见联系人
const visibleContacts = computed(() => {
  return companyInfo.value?.contactList?.filter(contact => contact.mail || contact.phone) || []
})

// 将生产能力数据按类别分组
function groupedProductionCapacity(productionCapacity: any) {
  if (!productionCapacity) {
    return {}
  }

  const grouped: Record<string, string[]> = {}

  // 递归处理对象，只收集最终的字符串值
  function processObject(obj: any, parentKey?: string) {
    if (!obj || typeof obj !== 'object') {
      return
    }

    Object.keys(obj).forEach((key) => {
      const value = obj[key]

      if (value) {
        // 如果值是数组，直接添加到分组中
        if (Array.isArray(value)) {
          if (!grouped[key]) {
            grouped[key] = []
          }
          grouped[key].push(...value.filter(item => typeof item === 'string'))
        }
        // 如果值是对象，递归处理，不展示当前key
        else if (typeof value === 'object') {
          processObject(value, key)
        }
        // 如果值是字符串，添加到分组中
        else if (typeof value === 'string') {
          if (!grouped[key]) {
            grouped[key] = []
          }
          grouped[key].push(value)
        }
      }
    })
  }

  // 处理新的JSON格式
  if (typeof productionCapacity === 'object') {
    processObject(productionCapacity)
  }

  return grouped
}

async function init() {
  // 从路由参数获取公司ID，如果没有则使用默认值
  const companyId = route.query.companyId ? Number(route.query.companyId) : 1
  await fetchCompanyDetail(companyId || 1)
  try {
    capabilities.value.forEach((a) => {
      // 如果 productionCap 是字符串，则解析为 JSON
      if (typeof a.productionCap === 'string') {
        a.productionCap = JSON.parse(a.productionCap)
      }
      // 如果已经是对象，则保持不变
    })
  }
  catch (error) {
    console.error('Failed to parse productionCap JSON:', error)
  }

  // 如果有能力数据，设置默认选中的一级品类
  if (capabilities.value.length > 0 && firstCategories.value.length > 0) {
    activeCategory.value = firstCategories.value[0].name || ''

    // 如果有二级品类，设置默认选中的二级品类
    if (subCategories.value.length > 0) {
      activeSubCategory.value = subCategories.value[0].name || ''
    }
  }
}
function openWebsite() {
  const website = businessInfo.value?.webSite
  if (!website)
    return
  // 确保网址有协议前缀，避免被当作相对路径
  const url = website.startsWith('http://') || website.startsWith('https://')
    ? website
    : `http://${website}`

  window.open(url, '_blank')
}
function handleAiReport(capability: Capability) {
  if (!capability.aiReport) {
    ElMessage.error('This Supplier Do not Generate Ai Report,Pls contact Admin!!!')
    return
  }
  router.push({
    path: localePath(`/report/${businessInfo.value?.id}`),
    query: {
      cateId: capability.categoryFirst,
      from: route.query.from || 'search', // 传递当前页面的来源信息
    },
  })
}
// 使用全局搜索状态
const useSearch = useSearchFlow()
function handleSearch() {
  // 打开新页面
  // if (!useSearch.searchText)
  //   return
  // window.open(router.resolve({ path: localePath('search'), query: { keyword: useSearch.searchText } }).href, '_blank')
  router.push({
    path: localePath('search'),
    query: {
      keyword: useSearch.searchText,
    },
  })
}
// 使用组合式API管理用户收藏公司记录
const userSavedCompanies = useSavedCompanies()
async function saveSupplier(supplierId: number) {
  try {
    // Check if the company is already saved
    if (userSavedCompanies.isCompanySaved(supplierId)) {
      // If already saved, remove from favorites
      const result = await userSavedCompanies.removeSavedCompany(supplierId)
      if (result) {
        ElMessage.success('Removed from favorites')
      }
    }
    else {
      // If not saved, add to favorites
      const result = await userSavedCompanies.saveCompany(supplierId)
      if (result) {
        ElMessage.success('Added to favorites')
      }
    }
  }
  catch (error) {
    console.error('Favorite operation failed:', error)
    ElMessage.error('Favorite operation failed')
  }
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="bg-#F8F8F8">
    <SearchHeader v-model="useSearch.searchText" @search="handleSearch" />
    <div v-if="loading" class="p-10 text-center">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="error" class="text-red-500 p-10 text-center">
      {{ error }}
    </div>
    <el-affix v-else :offset="88" class="bg-white">
      <div class="supplier-cards">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container px-6 py-3 flex items-center">
          <!-- Back按钮 -->
          <el-button
            link
            type="primary"
            class="text-gray-600 mr-4 hover:text-primary"
            @click="goBack"
          >
            <Icon name="ep:arrow-left" class="mr-1" size="16" />
            Back
          </el-button>

          <!-- 分隔符 -->
          <span class="text-gray-400 mr-4">|</span>

          <!-- 面包屑路径 -->
          <el-breadcrumb separator="/" class="flex-1">
            <el-breadcrumb-item
              v-for="(item, index) in breadcrumbItems"
              :key="index"
              :to="item.to"
              :class="{ 'breadcrumb-current': index === breadcrumbItems.length - 1 }"
            >
              {{ item.label }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="supplier-card mb-10px pb-4 pl-10% rounded-lg bg-white overflow-hidden">
          <!-- 卡片头部信息 -->
          <div class="p-6 pb-2 flex">
            <!-- 公司logo -->
            <div class="flex-[0_0_110px]">
              <div class="logo-container p-2 border border-gray-200 rounded-lg flex h-28 w-28 items-center justify-center">
                <img
                  v-if="logoAlbumUrls.length > 0 && logoAlbumUrls[0]"
                  :src="logoAlbumUrls[0]"
                  alt="Company logo"
                  class="max-h-full max-w-full object-contain"
                >
                <!--                <div v-else class="text-gray-400 bg-gray-200 flex h-full w-full items-center justify-center"> -->
                <!--                  Logo -->
                <!--                </div> -->
              </div>
            </div>

            <!-- 公司信息 -->
            <div class="ml-6 flex-1">
              <div class="flex items-start justify-between">
                <div>
                  <!-- 公司名和认证标签 -->
                  <div class="mb-3 flex items-center">
                    <h3 class="text-xl text-gray-800 font-medium">
                      {{ companyInfo?.name }}
                    </h3>
                    <img v-if="businessInfo?.esicSource === 'esic_vetted'" src="~/assets/images/esic-tag.png" alt="Logo" class="logo-image ml-2">
                  </div>

                  <!-- 基础信息行 -->
                  <el-space spacer="|" class="text-sm text-[#929292] mb-3 flex flex-wrap">
                    <!-- 位置 -->
                    <div v-if="businessInfo?.country" class="mr-3 flex items-center">
                      <span class="mr-1">Location</span>
                      <span class="text-[#383838]">
                        <Icon :name="`flagpack:${getCountryRegionCodeById(businessInfo?.country).toLowerCase()}`" width="32" height="24" />
                        {{ getCountryRegionNameById(businessInfo?.country) }}
                      </span>
                    </div>

                    <!-- 成立年份 -->
                    <div v-if="businessInfo?.establishAt" class="mr-3 flex items-center">
                      <span class="mr-1">Year Established</span>
                      <span class="text-[#383838]">{{ businessInfo?.establishAt ? new Date(businessInfo.establishAt).getFullYear() : '' }}</span>
                    </div>

                    <!-- 年销售额 -->
                    <div v-if="businessInfo?.annualSales" class="mr-3 flex items-center">
                      <span class="mr-1">Annual Sales</span>
                      <span class="text-[#383838]">{{ businessInfo?.annualSales ? `${businessInfo.annualSales.toLocaleString()} ${businessInfo.annualSalesCurrency}` : '' }}</span>
                    </div>

                    <!-- 员工数 -->
                    <div v-if="businessInfo?.allStaff" class="flex items-center">
                      <span class="mr-1">No. of employee</span>
                      <span class="text-[#383838]">{{ businessInfo?.allStaff }}</span>
                    </div>
                  </el-space>

                  <!-- 行业 -->
                  <div v-if="businessInfo?.industryList" class="text-sm mb-3">
                    <span class="text-[#929292] mr-1">Industry</span>
                    <span class="text-gray-700">
                      {{ getDictLabel(DICT_TYPE.SAAS_COMPANY_INDUSTRIES_INDUSTRY_CODE, businessInfo?.industryList)?.split(',').join('、') }}
                    </span>
                  </div>

                  <!-- 主要客户 -->
                  <div v-if="businessInfo?.majorClientList" class="text-sm">
                    <span class="text-[#929292] mr-1">Major Clients</span>
                    <span class="text-[#383838]">{{ businessInfo?.majorClientList?.join('、') }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex">
                  <el-button
                    v-if="!userSavedCompanies.isCompanySaved(Number(companyInfo?.companyId))"
                    plain
                    style="color: #4996B8;border-color: #4996B8"
                    @click.stop="saveSupplier(Number(companyInfo?.companyId))"
                  >
                    <template #icon>
                      <Icon name="ant-design:heart-outlined" size="16" />
                    </template>
                    Save
                  </el-button>
                  <el-button
                    v-else
                    type="primary"
                    style="background-color: #4996B8;border-color: #4996B8"
                    @click.stop="saveSupplier(Number(companyInfo?.companyId))"
                  >
                    <template #icon>
                      <Icon name="ant-design:heart-filled" size="16" />
                    </template>
                    Saved
                  </el-button>
                </div>
              </div>

              <div class="mt-2 relative">
                <div v-if="businessInfo?.webSite && businessInfo?.majorClientList && businessInfo?.industryList " class="border-t border-gray-200 left-0 right-0 absolute" style="margin-left: -100vw; margin-right: -100vw;" />
                <div class="pt-2 break-all">
                  {{ businessInfo?.remark }}
                </div>
              </div>
              <div v-if="businessInfo?.webSite" class="flex items-center">
                <el-link type="info" @click="openWebsite()">
                  {{ businessInfo?.webSite }}
                </el-link>
                <Icon class="color-#4996B8 ml-2" name="icon-park-outline:share" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-affix>

    <div v-if="!loading && !error" class="mt-2">
      <div class="px-10% flex gap-4">
        <div class="flex-[1_1_66.6%]">
          <!-- Business Info 部分 -->
          <div class="mb-4 p-6 rounded-lg bg-white">
            <h3 class="text-lg text-gray-800 font-medium mb-4">
              Business Info
            </h3>
            <el-descriptions :column="1" class="pl-164px">
              <el-descriptions-item label="Legal Representative" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.legalPerson">{{ businessInfo.legalPerson }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="Registered Capital" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.registerCapital">{{ businessInfo.registerCapital }} {{ businessInfo.registerCapitalCurrency }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="Operating Status" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.operateStatus">{{ businessInfo.operateStatus }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="VAT No." label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.vatNumber">{{ businessInfo.vatNumber }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="Business Type" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.businessType">{{ getDictLabel(DICT_TYPE.SAAS_COMPANY_BUSINESS_TYPES_BUSINESS_TYPE, businessInfo.businessType) }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="Registered Address" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.registeredAddress">{{ businessInfo.registeredAddress }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="Site Area" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.siteArea">{{ businessInfo.siteArea }} {{ businessInfo.siteAreaUnit }}</span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
              <el-descriptions-item label="Floor Space" label-class-name="business-info-label" class-name="business-info-content">
                <span v-if="businessInfo?.totalFloorAreaSqm">{{ businessInfo.totalFloorAreaSqm }}
                  {{ getDictLabel(DICT_TYPE.SAAS_UNIT_AREA, businessInfo.totalFloorAreaSqmUnit) }}
                </span>
                <span v-else class="text-gray-400">No data available</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- Categories & Manufacturing Capabilities 部分 -->
          <div class="mb-4 p-6 rounded-lg bg-white">
            <h3 class="text-lg text-gray-800 font-medium mb-4">
              Categories & Manufacturing Capabilities
            </h3>

            <!-- 上方一级品类选择 -->
            <div class="px-2 bg-#F8F8F8">
              <div class="mb-4 border-b border-gray-200 bg-#F8F8F8">
                <div class="mx--2 bg-white flex">
                  <div
                    v-for="category in firstCategories"
                    :key="String(category.id)"
                    class="mr-4 px-4 py-2 rd-t cursor-pointer"
                    :class="[category.name === activeCategory ? ' color-#28738E font-bold bg-#F8F8F8 mb--1 b-b-1 b-color-#f8f8f8 ' : 'text-gray-600 bg-white']"
                    @click="activeCategory = category.name"
                  >
                    {{ category.name }}
                  </div>
                </div>
              </div>

              <div class="flex">
                <!-- 左侧二级品类选择 -->
                <div class="px-2 border-r border-gray-200 flex-shrink-0 w-48">
                  <div class="flex flex-col">
                    <div
                      v-for="subcategory in subCategories"
                      :key="String(subcategory.id)"
                      class="mb-3 px-4 py-2 cursor-pointer"
                      :class="[subcategory.name === activeSubCategory ? 'bg-white color-#28738E font-bold rounded-md' : 'text-gray-600']"
                      @click="activeSubCategory = subcategory.name"
                    >
                      {{ subcategory.name }}
                    </div>
                  </div>
                </div>

                <!-- 右侧内容区域 -->
                <div class="ml-2 mt--2 py-4 pl-6 bg-white flex-1">
                  <!-- 从API获取的能力信息 -->
                  <div v-if="filteredCapabilities.length > 0">
                    <div v-for="capability in filteredCapabilities" :key="capability.id" class="mb-6">
                      <div class="mb-6 pb-6 border-b border-gray-200">
                        <h4 class="text-base text-gray-700 font-medium mb-3 flex justify-between">
                          <span>Process Capability</span>
                          <el-button
                            type="info"
                            class="mr-4"
                            plain @click="handleAiReport(capability)"
                          >
                            ESiC Report
                          </el-button>
                        </h4>
                        <p>
                          {{ capability.extIds?.map(item => getCategoryNameById(item)).filter(a => a).join('、 ') }}
                        </p>
                      </div>

                      <div v-if="capability.productionCap">
                        <h4 class="text-base text-gray-700 font-medium mb-3">
                          Production Capacity
                        </h4>
                        <!-- 按类别分组显示生产能力 -->
                        <div
                          v-for="(group, categoryKey) in groupedProductionCapacity(capability.productionCap)" :key="categoryKey"
                          class="mb-4 border-b border-gray-200"
                        >
                          <div class="mb-2 flex items-start">
                            <div class="text-gray-500 font-medium mr-4 pr-1 text-right min-w-34 w-120px">
                              {{ categoryKey }}
                            </div>
                            <span class="flex-1">{{ group.join('、') }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <p class="text-gray-400 py-10 text-center">
                      No capability information available
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quality Certification 部分 -->
          <div class="mb-4 p-6 rounded-lg bg-white">
            <h3 class="text-lg text-gray-800 font-medium mb-4">
              Quality Certification
            </h3>

            <div class="gap-4 grid grid-cols-5">
              <!-- 从API获取的认证信息 -->
              <div v-for="cert in qualityCerts" :key="cert.id" class="p-4 rounded-lg bg-gray-50">
                <div class="mb-2 flex items-center justify-center">
                  <el-image
                    :src="getDictExtendValue(DICT_TYPE.SAAS_COMPANY_QUALIFICATIONS_TYPE_QUALITY, cert.qualityType)"
                    class="h-20 w-20"
                  />
                  <!--                  {{getDictExtendValue(DICT_TYPE.SAAS_COMPANY_QUALIFICATIONS_TYPE_QUALITY, cert.qualityType)}} -->
                  <!--                  <div class="rounded bg-gray-300 flex h-16 w-16 items-center justify-center"> -->
                  <!--                    {{ cert.qualityType }} -->
                  <!--                  </div> -->
                </div>
                <div v-if="cert.validEndTime" class="text-xs text-gray-500 flex justify-center">
                  Validity Period {{ cert.validEndTime ? new Date(cert.validEndTime).toISOString().split('T')[0] : 'N/A' }}
                </div>
              </div>

              <div v-if="qualityCerts.length === 0" class="text-gray-400 py-5 text-center col-span-5">
                No certification information available
              </div>
            </div>
          </div>

          <!-- Management System 部分 -->
          <div class="mb-4 p-6 rounded-lg bg-white">
            <h3 class="text-lg text-gray-800 font-medium mb-4">
              Management System
            </h3>

            <ul class="pl-5">
              <!-- 从API获取的管理系统信息 -->
              <li v-for="system in managementSystems" :key="system.id" class="mb-3 flex items-center">
                <div class="mr-3 rounded-full bg-gray-400 h-2 w-2" />
                <span>{{ system.name }}
                  <span v-if="system.brand">
                    / {{ system.brand }}
                  </span>
                </span>
              </li>

              <li v-if="managementSystems.length === 0" class="text-gray-400 py-5 text-center">
                No management system information available
              </li>
            </ul>
          </div>

          <!-- Company Album 部分 -->
          <div class="mb-6 p-6 rounded-lg bg-white">
            <h3 class="text-lg text-gray-800 font-medium mb-4">
              Company Album
            </h3>

            <div class="image-gallery p-0 flex flex-wrap overflow-hidden">
              <!-- 从API获取的相册信息 -->
              <template v-if="companyAlbumUrls.length > 0">
                <el-image
                  v-for="(album, index) in albumInfos.filter(i => companyPhotoTypes.includes(i.businessType) && i.fileId)"
                  :key="album.id"
                  style="width: 160px; height: 120px"
                  :src="album.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="companyAlbumUrls"
                  show-progress
                  :initial-index="index"
                  fit="contain"
                  class="mx-1"
                />
              </template>

              <div v-else class="text-gray-400 py-10 text-center w-full">
                No album images available
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Supplier 部分 -->
        <div class="flex-[0_0_336px]">
          <div>
            <div class="mb-4 p-6 rounded-lg bg-white">
              <h3 class="text-lg text-gray-800 font-bold mb-4">
                Contact Supplier
              </h3>
              <div class="mb-4">
                <!-- 从API获取的联系人信息 -->
                <div v-if="visibleContacts && visibleContacts.length > 0">
                  <div
                    v-for="(contact, index) in visibleContacts" :key="contact.id"
                    class="mb-4 pb-4 border-gray-200"
                    :class="{ 'border-b': index < visibleContacts.length - 1 }"
                  >
                    <div class="mb-2 flex items-center">
                      <span class="font-medium">{{ contact.name }}</span>
                      <span v-if="contact.title" class="text-sm text-gray-500 ml-2">| {{ contact.title }}</span>
                    </div>

                    <div v-if="contact.mail" class="mb-2 flex items-center">
                      <Icon name="mdi:email-outline" class="text-gray-400 mr-2" size="18" />
                      <span>{{ contact.mail }}</span>
                      <el-button class="color-#4996B8 ml-2" type="text" @click="copyToClipboard(contact.mail)">
                        Copy
                      </el-button>
                    </div>

                    <div v-if="contact.phone" class="flex items-center">
                      <Icon name="mdi:phone-outline" class="text-gray-400 mr-2" size="18" />
                      <span>{{ contact.phone }}</span>
                      <el-button class="color-#4996B8 ml-2" type="text" @click="copyToClipboard(contact.phone)">
                        Copy
                      </el-button>
                    </div>
                  </div>
                </div>
                <div v-else class="text-gray-400 py-5 text-center">
                  No contact information available
                </div>
              </div>
            </div>

            <div class="mb-4">
              <div class="gradient-header p-4 rounded-t-lg flex flex-col">
                <img src="~/assets/images/logo.png" alt="ESIC Logo" class="mr-4 h-60px w-63px">
                <div class="font-bold ml-1 flex-1">
                  <div class="font-size-17px text-white !color-#383838">
                    <div>
                      Contact an ESiC Professional
                    </div>
                    <div>
                      Procurement Outsourcing Services
                    </div>
                  </div>
                </div>
              </div>

              <div class="service-details">
                <div class="service-list">
                  <div class="service-item">
                    <span class="service-bullet" />
                    <span>Supplier Onboarding</span>
                  </div>
                  <div class="service-item">
                    <span class="service-bullet" />
                    <span>Inquiry & Biz Negotiation</span>
                  </div>
                  <div class="service-item">
                    <span class="service-bullet" />
                    <span>NPI Support</span>
                  </div>
                  <div class="service-item">
                    <span class="service-bullet" />
                    <span>Quality & Coaching</span>
                  </div>
                </div>
                <div class="contact-button" @click="router.push(localePath('contact'))">
                  Contact ESiC
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.logo-image {
  height: 24px;
  width: 98px;
}

:deep(.business-info-label) {
  width: 180px;
  display: inline-block;
  color: #929292 !important;
}

:deep(.business-info-content) {
  padding: 12px 16px;
  color: #565656;
}

:deep(.el-descriptions__body) {
  width: 100%;
}

:deep(.el-descriptions__table) {
  width: 100%;
}

:deep(.el-descriptions__cell) {
  box-sizing: border-box;
}

.service-details {
  width: 100%;
  background: white;
  border-radius: 8px;
  z-index: 10;
  padding: 16px;
  animation: fadeIn 0.2s ease-in-out;
}
/* 服务列表 */
.service-list {
  margin-bottom: 16px;
}

/* 服务项目 */
.service-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #314564;
}

/* 服务项目前的圆点 */
.service-bullet {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4996b8;
  margin-right: 12px;
}

/* 联系按钮 */
.contact-button {
  background: #ffffff;
  border: 1px solid #314564;
  border-radius: 8px;
  color: #314564;
  padding: 10px;
  text-align: center;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.gradient-header {
  min-height: 80px;
  background: linear-gradient(90deg, rgba(73, 150, 184, 0.2) 0%, rgba(73, 150, 184, 0) 70%), #f2f4f9;
}

/* 面包屑样式 */
.breadcrumb-container {
  background: white;
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  color: #4996b8 !important;
  font-weight: 500;
}

:deep(.breadcrumb-current) {
  color: #374151 !important;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #374151 !important;
  font-weight: 500;
}

:deep(.el-breadcrumb__item:not(:last-child) .el-breadcrumb__inner:hover) {
  color: #374151 !important;
}
.supplier-card {
  padding-right: calc((80% - 2rem) / 3 - 336px + 10%);
}
</style>
