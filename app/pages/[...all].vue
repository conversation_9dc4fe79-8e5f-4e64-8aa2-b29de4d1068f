<script setup lang="ts">
const router = useRouter()

definePageMeta({
  layout: 'home',
})

// 动画控制
const isVisible = ref(false)

onMounted(() => {
  setTimeout(() => {
    isVisible.value = true
  }, 100)
})

// 返回首页
function goHome() {
  navigateTo('/')
}

// 返回上一页
function goBack() {
  if (window.history.length > 1) {
    router.back()
  }
  else {
    goHome()
  }
}
</script>

<template>
  <main class="bg-gradient-to-br px-4 flex min-h-screen items-center justify-center from-blue-50 to-indigo-100">
    <div
      class="mx-auto text-center max-w-md transform transition-all duration-1000 ease-out"
      :class="isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'"
    >
      <!-- 404 数字 -->
      <div class="mb-8 relative">
        <h1 class="bg-gradient-to-r text-9xl text-transparent font-bold select-none from-blue-600 to-purple-600 bg-clip-text">
          404
        </h1>
        <div class="text-9xl text-blue-200 font-bold translate-x-2 translate-y-2 transform inset-0 absolute -z-10">
          404
        </div>
      </div>

      <!-- 标题和描述 -->
      <div class="mb-8">
        <h2 class="text-2xl text-gray-800 font-bold mb-3">
          Oops! Page Not Found
        </h2>
        <p class="text-gray-600 leading-relaxed">
          The page you're looking for seems to have wandered off into the digital void.
          Don't worry, it happens to the best of us!
        </p>
      </div>

      <!-- 按钮组 -->
      <div class="flex flex-col gap-4 justify-center sm:flex-row">
        <button
          class="focus:outline-none text-gray-700 font-medium px-6 py-3 border-2 border-gray-300 rounded-lg bg-white shadow-lg transform transition-all duration-200 hover:border-gray-400 focus:ring-4 focus:ring-gray-300 hover:shadow-xl hover:-translate-y-0.5"
          @click="goHome"
        >
          <div class="flex gap-2 items-center justify-center">
            <Icon name="carbon:arrow-left" class="text-lg" />
            <span>Go Home</span>
          </div>
        </button>
      </div>

      <!-- 装饰性元素 -->
      <div class="mt-12 flex justify-center space-x-2">
        <div class="rounded-full bg-blue-400 h-2 w-2 animate-bounce" style="animation-delay: 0ms" />
        <div class="rounded-full bg-purple-400 h-2 w-2 animate-bounce" style="animation-delay: 150ms" />
        <div class="rounded-full bg-pink-400 h-2 w-2 animate-bounce" style="animation-delay: 300ms" />
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="pointer-events-none inset-0 absolute overflow-hidden">
      <!-- 浮动圆圈 -->
      <div class="top-1/4 left-1/4 rounded-full bg-blue-200 opacity-20 h-32 w-32 absolute animate-pulse" />
      <div class="top-3/4 right-1/4 rounded-full bg-purple-200 opacity-20 h-24 w-24 absolute animate-pulse" style="animation-delay: 1s" />
      <div class="top-1/2 left-3/4 rounded-full bg-pink-200 opacity-20 h-16 w-16 absolute animate-pulse" style="animation-delay: 2s" />
    </div>
  </main>
</template>
