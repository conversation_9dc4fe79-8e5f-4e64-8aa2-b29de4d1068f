<script setup lang="ts">
// 示例供应商数据
import dayjs from 'dayjs'
import { computed } from 'vue'
import { useSavedCompanies } from '~/composables/api/modules/company'
import { useSearchFlow } from '~/composables/api/modules/search'
import DICT_TYPE from '~/constants/dictType'
import emitter from '~/utils/emitter'

const useUser = useUserStore()
const userSavedCompanies = useSavedCompanies()
const router = useRouter()
const localePath = useLocalePath()
const suppliers = ref<any[]>([

])
const useSearch = useSearchFlow()
const { getDictLabel } = useCacheStore()

function toSupplierPage(supplier) {
  if (!useUser.isLoggedIn) {
    emitter.emit('open-login')
    return
  }
  router.push({
    path: localePath('supplier'),
    query: {
      companyId: supplier.supplier_id,
      from: 'collection',
    },
  })
}
async function saveSupplier(supplierId: number) {
  try {
    // Check if the company is already saved
    if (userSavedCompanies.isCompanySaved(supplierId)) {
      // If already saved, remove from favorites
      const result = await userSavedCompanies.removeSavedCompany(supplierId)
      if (result) {
        ElMessage.success('Removed from favorites')
      }
    }
    else {
      // If not saved, add to favorites
      const result = await userSavedCompanies.saveCompany(supplierId)
      if (result) {
        ElMessage.success('Added to favorites')
      }
    }
  }
  catch (error) {
    console.error('Favorite operation failed:', error)
    ElMessage.error('Favorite operation failed')
  }
}
const isLoading = ref(false)

async function doSearch(from = 1) {
  useSearch.from = from
  if (userSavedCompanies.savedCompanies.value?.companyId?.length == 0) {
    return
  }
  isLoading.value = true
  await useSearch.doSearch('', [
    {
      entity: 'supplier_id',
      value: userSavedCompanies.savedCompanies.value?.companyId?.join(','),
    },
  ])
  isLoading.value = false

  suppliers.value = useSearch.searchResults
  window.scrollTo(0, 0)
}
onMounted(async () => {
  await userSavedCompanies.loadSavedCompanies()
  if (userSavedCompanies.savedCompanies.value?.companyId?.length === 0) {
    return
  }
  doSearch()
})

const breadcrumbItems = computed(() => {
  const items = [
    { label: 'Home', to: { path: localePath('/') } },
  ]

  items.push({
    label: 'Search',
    to: {
      path: localePath('/search'),
    },
  })

  // 添加当前公司名称
  items.push({ label: 'Saved suppliers', to: null })

  return items
})

// 返回上一页功能
function goBack() {
  if (window.history.length > 1) {
    router.back()
  }
  else {
    router.push(localePath('/'))
  }
}
</script>

<template>
  <div class="pl-8.3% pr-23.9%">
    <div class="breadcrumb-container ml--100px px-6 py-3 flex items-center">
      <!-- Back按钮 -->
      <el-button
        link
        type="primary"
        class="text-gray-600 mr-4 hover:text-primary"
        @click="goBack"
      >
        <Icon name="ep:arrow-left" class="mr-1" size="16" />
        Back
      </el-button>
      <span class="text-gray-400 mr-4">|</span>

      <el-breadcrumb separator="/" class="flex-1">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbItems"
          :key="index"
          :to="item.to"
          :class="{ 'breadcrumb-current': index === breadcrumbItems.length - 1 }"
        >
          {{ item.label }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="font-size-28px py-10px">
      Saved suppliers({{ userSavedCompanies.savedCompanies?.value?.companyId?.length || 0 }})
    </div>

    <div>
      <div v-if="suppliers.length > 0" class="supplier-cards">
        <div
          v-for="supplier in suppliers" :key="supplier.supplier_id"
          class="supplier-card mb-10px pb-4 rounded-lg bg-white cursor-pointer shadow-sm transition-all duration-200 overflow-hidden hover:shadow-md"
          @click="toSupplierPage(supplier)"
        >
          <!-- 卡片头部信息 -->
          <div class="p-6 pb-2 flex">
            <!-- 公司logo -->
            <div class="flex-[0_0_110px]">
              <div class="logo-container p-2 border border-gray-200 rounded-lg flex h-28 w-28 items-center justify-center">
                <img :src="supplier.supplier_logo" alt="Company logo" class="max-h-full max-w-full">
                <!--                        <div v-else class="text-gray-400 bg-gray-200 flex h-full w-full items-center justify-center"> -->
                <!--                          Logo -->
                <!--                        </div> -->
              </div>
            </div>

            <!-- 公司信息 -->
            <div class="ml-6 flex-1">
              <div class="flex items-start justify-between">
                <div class="text-[#383838]">
                  <!-- 公司名和认证标签 -->
                  <div class="mb-3 flex items-center">
                    <h3 class="text-xl text-gray-800 font-bold font-medium">
                      {{ supplier.supplier_name }}
                    </h3>
                    <img v-if="supplier.esic_votted" src="~/assets/images/esic-tag.png" alt="Logo" class="logo-image ml-2">
                  </div>

                  <!-- 基础信息行 -->
                  <el-space spacer="|" class="text-sm text-[#929292] mb-3 flex flex-wrap">
                    <!-- 位置 -->
                    <div v-if="supplier.location" class="mr-3 flex items-center">
                      <span class="mr-1">Location</span>
                      <!--                              <span class="flag-icon mr-1">{{ supplier.locationFlag }}</span> -->
                      <span class="text-[#383838]">{{ supplier.location }}</span>
                    </div>

                    <!-- 成立年份 -->
                    <div v-if="supplier.established_time" class="mr-3 flex items-center">
                      <span class="mr-1">Year Established</span>
                      <span class="text-[#383838]">{{
                        dayjs(supplier.established_time).format('YYYY-MM-DD')
                      }}</span>
                    </div>

                    <!-- 年销售额 -->
                    <div v-if="supplier.annual_sales" class="mr-3 flex items-center">
                      <span class="mr-1">Annual Sales</span>
                      <span class="text-[#383838]">{{ supplier.annual_sales.toLocaleString() }}</span>
                    </div>

                    <!-- 员工数 -->
                    <div v-if="supplier.employee_count" class="flex items-center">
                      <span class="mr-1">No. of employee</span>
                      <span class="text-[#383838]">{{ supplier.employee_count }}</span>
                    </div>
                  </el-space>

                  <!-- 行业 -->
                  <div v-if="supplier.industry" class="text-sm text-[#929292] mb-3">
                    <span class="mr-1">Industry</span>
                    <span class="text-[#383838]">
                      {{  supplier.industry?.join('、') }}
                    </span>
                  </div>

                  <!-- 主要客户 -->
                  <div v-if="supplier.major_clients" class="text-sm text-[#929292]">
                    <span class="mr-1">Major Clients</span>
                    <span class="text-[#383838]">{{ supplier.major_clients.join('、') }}</span>
                  </div>
                </div>
                <!-- 操作按钮 -->
                <div class="flex">
                  <span v-if="useUser.isLoggedIn">
                    <el-button
                      v-if="!userSavedCompanies.isCompanySaved(Number(supplier.supplier_id))"
                      plain
                      style="color: #4996B8;border-color: #4996B8"
                      @click.stop="saveSupplier(Number(supplier.supplier_id))"
                    >
                      <template #icon>
                        <Icon name="ant-design:heart-outlined" size="16" />
                      </template>
                      Save
                    </el-button>
                    <el-button
                      v-else
                      type="primary"
                      style="background-color: #4996B8;border-color: #4996B8"
                      @click.stop="saveSupplier(Number(supplier.supplier_id))"
                    >
                      <template #icon>
                        <Icon name="ant-design:heart-filled" size="16" />
                      </template>
                      Saved
                    </el-button>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品图片展示 -->
          <div v-if="supplier.supplier_images" class="image-gallery p-0 pl-157px flex overflow-hidden" @click.stop>
            <el-image
              v-for="(image, index) in supplier.supplier_images"
              :key="index"
              style="width: 107px; height: 80px"
              :src="image"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="supplier.supplier_images"
              show-progress
              :initial-index="index"
              fit="contain"
              class="mx-1"
            />
          </div>
        </div>
        <div
          v-if="useSearch.size < useSearch.total"
          class="flex justify-center"
        >
          <!-- 分页组件 -->
          <el-pagination
            background
            layout="prev, pager, next"
            :page-size="useSearch.size"
            :current-page="useSearch.from"
            :total="userSavedCompanies.savedCompanies.value?.companyId.length"
            class="mt-4"
            @update:current-page="doSearch"
          />
        </div>
      </div>
      <div v-else class="bg-white flex min-h-screen items-center justify-center">
        <div class="text-center">
          <!-- 自定义空状态图标 -->
          <div class="mb-6 flex justify-center">
            <img src="/assets/images/compare-no-result.png" class="h-200px w-200px" alt="">
          </div>

          <h3 class="text-xl text-gray-800 font-medium mb-4">
            No supplier here
          </h3>
          <p class="text-gray-600 mb-6">
            You may consider to save supplier by searching
          </p>

          <el-button
            type="primary"
            plain
            size="large"
            @click="router.push(localePath('search'))"
          >
            Search for suppliers
          </el-button>
        </div>
      </div>
    </div>
    <el-backtop :right="50" :bottom="80" />

  </div>
</template>

<style scoped>
.logo-image {
  height: 24px;
  width: 98px;
}

/* 供应商卡片容器 */
.supplier-cards {
  display: flex;
  flex-direction: column;
  //gap: 16px;
}

/* 供应商卡片样式 */
.supplier-card {
  min-width: 800px;
  max-width: 100%;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.supplier-card:hover {
  border-color: #d1d5db;
}

/* Logo 容器优化 */
.logo-container {
  min-width: 112px;
  min-height: 112px;
  background-color: #fafafa;
}

/* 公司信息区域 */
.supplier-card .ml-6 {
  min-width: 0; /* 允许 flex 子元素收缩 */
}

/* 图片画廊优化 */
.image-gallery {
  min-height: 80px;
  padding-bottom: 16px;
}

.image-gallery .el-image {
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .supplier-card {
    min-width: 700px;
  }
}

@media (max-width: 992px) {
  .supplier-card {
    min-width: 600px;
  }

  .image-gallery {
    padding-left: 120px !important;
  }
}

@media (max-width: 768px) {
  .supplier-card {
    min-width: 500px;
  }

  .supplier-card .p-6 {
    padding: 16px !important;
  }

  .logo-container {
    min-width: 80px;
    min-height: 80px;
  }

  .image-gallery {
    padding-left: 100px !important;
  }

  .image-gallery .el-image {
    width: 80px !important;
    height: 60px !important;
    min-width: 80px !important;
  }
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  color: #4996b8 !important;
  font-weight: 500;
}

:deep(.breadcrumb-current) {
  color: #374151 !important;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #374151 !important;
  font-weight: 500;
}

:deep(.el-breadcrumb__item:not(:last-child) .el-breadcrumb__inner:hover) {
  color: #374151 !important;
}
</style>
