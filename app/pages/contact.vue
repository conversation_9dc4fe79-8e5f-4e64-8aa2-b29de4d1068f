<script setup lang="ts">
import type { UploadFile } from 'element-plus'
import type { SearchApi } from '~/composables/api/types'
import { Delete } from '@element-plus/icons-vue'
import { useContactESiC, useCurrentUser, useUploadFile } from '~/composables/api/modules'
import { useCompanyTagSearch, useCompareUserRel } from '~/composables/api/modules/company'
import { useSearchFlow } from '~/composables/api/modules/search'

// 添加提交成功状态控制
const isSubmitSuccess = ref(false)

const cacheStore = useCacheStore()
// 表单数据与校验规则
const form = reactive({
  title: '',
  message: '',
  attachment: [] as UploadFile[],
  firstName: '',
  lastName: '',
  email: '',
  company: '',
})

const rules = {
  title: [{ required: true, message: 'Project title is required', trigger: 'blur' }],
  message: [{ required: true, message: 'Message is required', trigger: 'blur' }],
  firstName: [{ required: true, message: 'Name is required', trigger: 'blur' }],
  lastName: [{ required: true, message: 'lastName is required', trigger: 'blur' }],
  email: [
    { required: true, message: 'Email is required', trigger: 'blur' },
    { type: 'email' as any, message: 'Invalid email address', trigger: ['blur', 'change'] },
  ],
}

const formRef = ref()

// 右侧表单数据
const additionalData = reactive({
  suppliers: [] as number[],
  selectInfo: '',
  cids: [] as number[][],
  tags: [] as string[],
  supplierSearch: '',
})

// 搜索相关状态
const useSearch = useSearchFlow()
const searchResults = ref<SearchApi.SupplierInfo[]>([])
const isSearching = ref(false)

// 已添加的供应商详细信息
const addedSuppliers = ref<SearchApi.SupplierInfo[]>([])

// tag 搜索相关状态
const tagSearchResults = ref<string[]>([])
const isTagSearching = ref(false)

// 获取路由信息
const router = useRouter()
const route = useRoute()
const currentUser = useCurrentUser()
const useUser = useUserStore()

// 使用组合式API管理用户比对公司记录
const userCompanyCompare = useCompareUserRel()
// 自定义过滤方法，忽略大小写
function categoryFilterMethod(node: any, keyword: string) {
  if (!keyword)
    return true
  const text = node.text || node.label || ''
  return text.toLowerCase().includes(keyword.toLowerCase())
}

// 根据分类名称获取分类ID路径的辅助函数
function getCategoryIdPathByName(categoryName: string): number[] | null {
  const findCategoryInTree = (categories: any[], name: string, path: number[] = []): number[] | null => {
    for (const category of categories) {
      const currentPath = [...path, category.id]

      if (category.name === name) {
        return currentPath
      }

      if (category.children && category.children.length > 0) {
        const result = findCategoryInTree(category.children, name, currentPath)
        if (result) {
          return result
        }
      }
    }
    return null
  }

  return findCategoryInTree(cacheStore.getCategoryTree, categoryName)
}

// 处理从搜索页面传递过来的对比供应商ID
async function handleCompareSuppliers() {
  try {
    // 已登录用户自动带入 Email、Name、Company Name
    if (useUser.isLoggedIn) {
      await currentUser.loadUserInfo()
      form.email = currentUser.user.value?.email || ''
      form.firstName = currentUser.user.value?.nickname || ''
      form.company = currentUser.user.value?.companyName || ''
    }

    // 从 useSearch store 中获取选中的分类，并转换为ID路径添加到 cids 中
    if (useSearch.selectedCategories.length > 0) {
      useSearch.selectedCategories.forEach((categoryName) => {
        const categoryIdPath = getCategoryIdPathByName(categoryName)
        if (categoryIdPath) {
          // 检查是否已经存在相同的路径
          const pathExists = additionalData.cids.some(existingPath =>
            JSON.stringify(existingPath) === JSON.stringify(categoryIdPath),
          )
          if (!pathExists) {
            additionalData.cids.push(categoryIdPath)
          }
        }
      })
    }
    if (useSearch.searchText) {
      additionalData.selectInfo = useSearch.searchText
    }

    if (useSearch.selectedTags.length > 0) {
      // 将选中的标签添加到 additionalData.tags 中
      useSearch.selectedTags.forEach((tag) => {
        if (!additionalData.tags.includes(tag)) {
          additionalData.tags.push(tag)
        }
      })
    }
    if (useUser.isLoggedIn) {
    // 从 useCompareUserRel 中获取比较列表中的公司ID
      await userCompanyCompare.loadCompareUserRel()

      const compareList = userCompanyCompare.compareUserRel.value?.companyId || []

      if (compareList.length > 0) {
      // 使用搜索API获取供应商详细信息
        const success = await useSearch.doOriginSearch('', [
          {
            entity: 'supplier_id',
            value: compareList.join(','),
          },
        ])

        if (success && useSearch.searchResults.length > 0) {
          let addedCount = 0

          // 将获取到的供应商添加到已添加列表中
          useSearch.searchResults.forEach((supplier: SearchApi.SupplierInfo) => {
          // 检查是否已经添加过
            const isAlreadyAdded = addedSuppliers.value.some(s => s.supplier_name === supplier.supplier_name)

            if (!isAlreadyAdded) {
              addedSuppliers.value.push(supplier)
              // 同时添加到 suppliers 数组中（用于提交）
              if (supplier.supplier_id) {
                additionalData.suppliers.push(Number(supplier.supplier_id))
              }
              else {
              // 如果没有 supplier_id，使用 supplier_name 的哈希值作为 ID
                const hashId = Math.abs(supplier.supplier_name.split('').reduce((acc, char) => (acc * 31 + char.charCodeAt(0)) & 0xFFFFFFFF, 0)) % 100000
                additionalData.suppliers.push(hashId)
              }
              addedCount++
            }
          })
        }
      }
      else {
        ElMessage.warning('Failed to load supplier details from comparison list')
      }
    }
  }
  catch (error) {
    console.error('Error handling compare suppliers:', error)
    ElMessage.error('Failed to load suppliers from comparison list')
  }
}

// 提交
async function handleSubmit() {
  (formRef.value as any).validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 准备文件ID数组
        const fileIds = form.attachment
          .filter((file) => {
            // 检查文件是否有响应且响应中包含data字段
            return file.response
              && typeof file.response === 'object'
              && 'data' in file.response
          })
          .map((file) => {
            // 安全地访问和转换file.response.data
            const response = file.response as { data: number | string }
            return typeof response.data === 'string'
              ? Number.parseInt(response.data, 10)
              : response.data.id
          })

        // 准备提交数据
        const contactParams = {
          projectTitle: form.title,
          message: form.message,
          fileIds,
          firstName: form.firstName,
          lastName: form.lastName,
          email: form.email,
          company: form.company,
          suppliers: additionalData.suppliers,
          selectInfo: additionalData.selectInfo,
          cids: additionalData.cids.map(a => a?.at(-1)),
          tags: additionalData.tags,
        }

        // 调用API提交数据
        const response = await useContactESiC(contactParams)

        if (response.code === 0) {
          // 显示成功页面而不是消息
          isSubmitSuccess.value = true
          window.scroll(0, 0)
          ElMessage.success('Message sent successfully')
        }
        else {
          ElMessage.error(response.msg || 'Submission failed')
        }
      }
      catch (err: any) {
        ElMessage.error('An error occurred during submission')
      }
    }
    else {
      ElMessage.error('Please check the form')
    }
  })
}

// 返回首页
function goBackToHome() {
  // 重置表单
  resetForm()
  // 重置成功状态
  // isSubmitSuccess.value = false
  // 可选：导航到首页
  navigateTo('/')
}

// 重置表单
function resetForm() {
  // (formRef.value as any).resetFields()
  form.attachment = []
  additionalData.suppliers = []
  additionalData.selectInfo = ''
  additionalData.cids = []
  additionalData.tags = []
  additionalData.supplierSearch = ''
  addedSuppliers.value = []
  searchResults.value = []
}

// 文件大小格式化函数
function formatFileSize(bytes: number | undefined): string {
  if (!bytes || bytes === 0)
    return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

// 删除文件处理函数
function handleRemoveFile(fileToRemove: UploadFile) {
  form.attachment = form.attachment.filter(file => file.uid !== fileToRemove.uid)
  // 注意：如果 useUploadFile 或 el-upload 有额外的状态管理，可能需要进一步处理
  // 例如，如果文件正在上传，可能需要调用 el-upload 实例的 abort 方法
}

function openFile(file: UploadFile) {
  window.open(file.url)
}

// 文件上传成功回调函数
function onSuccess(response: any, uploadFile: UploadFile, _uploadFiles: UploadFile[]) {
  // 检查响应是否成功
  if (response && response.code === 0 && response.data) {
    // 设置文件的URL
    uploadFile.url = response.data

    // 将上传成功的文件添加到表单的附件列表中
    if (!form.attachment.some(file => file.uid === uploadFile.uid)) {
      form.attachment.push(uploadFile)
    }

    ElMessage.success('文件上传成功')
  }
  else {
    // 上传失败处理
    ElMessage.error(`文件上传失败: ${response?.msg || '未知错误'}`)
    // 从文件列表中移除上传失败的文件
    form.attachment = form.attachment.filter(file => file.uid !== uploadFile.uid)
  }
}

// 搜索供应商
async function searchSuppliers(query: string) {
  if (!query.trim()) {
    searchResults.value = []
    return
  }

  isSearching.value = true
  try {
    const success = await useSearch.doSearch('', [
      {
        entity: 'supplier_name',
        value: query,
      },
    ])
    if (success) {
      searchResults.value = useSearch.searchResults.value
    }
    else {
      searchResults.value = []
      ElMessage.error('Search failed')
    }
  }
  catch (error) {
    console.error('Search error:', error)
    searchResults.value = []
    ElMessage.error('Search failed')
  }
  finally {
    isSearching.value = false
  }
}

// 处理供应商选择
function handleSupplierSelect(supplierName: string) {
  if (!supplierName)
    return

  // 从搜索结果中找到对应的供应商
  const supplier = searchResults.value.find(s => s.supplier_name === supplierName)
  if (!supplier)
    return

  // 检查是否已经添加过
  const isAlreadyAdded = addedSuppliers.value.some(s => s.supplier_name === supplier.supplier_name)

  if (!isAlreadyAdded) {
    addedSuppliers.value.push(supplier)
    // 同时添加到 suppliers 数组中（用于提交）
    if (supplier.supplier_id) {
      additionalData.suppliers.push(Number(supplier.supplier_id))
    }
    else {
      // 如果没有 supplier_id，使用 supplier_name 的哈希值作为 ID
      const hashId = Math.abs(supplier.supplier_name.split('').reduce((acc, char) => (acc * 31 + char.charCodeAt(0)) & 0xFFFFFFFF, 0)) % 100000
      additionalData.suppliers.push(hashId)
    }

    ElMessage.success(`Added ${supplier.supplier_name}`)
  }
  else {
    ElMessage.warning('This supplier is already added')
  }

  // 清空选择
  additionalData.supplierSearch = ''
}

// 移除供应商
function removeSupplier(supplier: SearchApi.SupplierInfo) {
  const index = addedSuppliers.value.findIndex(s =>
    (s.supplier_id && supplier.supplier_id && s.supplier_id === supplier.supplier_id)
    || s.supplier_name === supplier.supplier_name,
  )

  if (index !== -1) {
    addedSuppliers.value.splice(index, 1)

    // 同时从 suppliers 数组中移除对应的 ID
    let supplierIdToRemove: number
    if (supplier.supplier_id) {
      supplierIdToRemove = Number(supplier.supplier_id)
    }
    else {
      // 如果没有 supplier_id，计算哈希值
      supplierIdToRemove = Math.abs(supplier.supplier_name.split('').reduce((acc, char) => (acc * 31 + char.charCodeAt(0)) & 0xFFFFFFFF, 0)) % 100000
    }

    const supplierIdIndex = additionalData.suppliers.indexOf(supplierIdToRemove)
    if (supplierIdIndex !== -1) {
      additionalData.suppliers.splice(supplierIdIndex, 1)
    }

    ElMessage.success(`Removed ${supplier.supplier_name}`)
  }
}

// 搜索 tags
async function searchTags(query: string) {
  if (!query.trim()) {
    tagSearchResults.value = []
    return
  }

  isTagSearching.value = true
  try {
    const response = await useCompanyTagSearch(query.trim())
    if (response.code === 0 && response.data) {
      tagSearchResults.value = response.data
    }
    else {
      tagSearchResults.value = []
    }
  }
  catch (error) {
    console.error('Tag search error:', error)
    tagSearchResults.value = []
  }
  finally {
    isTagSearching.value = false
  }
}

// 组件挂载时处理对比供应商数据
onMounted(() => {
  handleCompareSuppliers()
})
</script>

<template>
  <div class="py-8 bg-#F8F8F8 min-h-screen">
    <div v-if="!isSubmitSuccess" class="mx-auto mb-6 max-w-1600px">
      <h1 class="text-2xl font-bold">
        Contact ESiC
      </h1>
    </div>
    <!-- 成功页面 -->
    <div v-if="isSubmitSuccess" class="mx-auto pt-12 text-center max-w-120">
      <div class="success-icon-container mb-6">
        <img src="@/assets/images/sent-success.png" alt="Success" class="mx-auto h-50 w-50">
      </div>
      <h2 class="text-2xl mb-2">
        Message sent
      </h2>
      <p class="text-gray-600 mb-8">
        Your details have been sent to ESiC.<br>
        We will get back to you via email within two business days.
      </p>
      <el-button type="primary" size="large" plain class="w-60%" @click="goBackToHome">
        Back to home page
      </el-button>
    </div>
    <div v-else class="mx-auto flex gap-3 max-w-1600px">
      <!-- 左侧表单 -->
      <div class="p-8 pb-15px rounded-lg bg-white flex-1 w-900px shadow relative">
        <h2 class="text-xl font-semibold mb-6">
          Let us know what you need
        </h2>
        <el-form ref="formRef" :model="form" :rules="rules" label-position="top" size="large" class="form-with-fixed-button">
          <el-form-item label="Project title" prop="title" required>
            <el-input v-model="form.title" placeholder="Enter title" />
          </el-form-item>
          <el-form-item label="Message" prop="message" required>
            <el-input
              v-model="form.message"
              type="textarea"
              :rows="4"
              maxlength="2000"
              show-word-limit
              placeholder="Please provide information and files about your project so we can assist you effectively."
            />
          </el-form-item>
          <el-form-item label="Attachment">
            <el-upload
              v-model:file-list="form.attachment"
              class="w-full"
              drag
              :with-credentials="true"
              :http-request="useUploadFile"
              action="#"
              :show-file-list="false"
              name="file"
              :on-success="onSuccess"
            >
              <div class="line-height-16px">
                <Icon name="ic:outline-file-upload" size="24" />
                <div>
                  Drag and drop or <span class="text-primary cursor-pointer">browse</span>
                </div>
              </div>
            </el-upload>
            <!-- 文件列表显示 -->
            <div v-if="form.attachment.length > 0" class="mt-4 w-full">
              <div
                v-for="fileItem in form.attachment"
                :key="fileItem.uid"
                class="file-item py-3 border-b border-gray-200 flex w-full items-center justify-between"
              >
                <el-button type="text" class="text-sm text-primary truncate !color-#4996B8" :title="fileItem.name" @click="openFile(fileItem)">
                  {{ fileItem.name }}
                </el-button>
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 mr-4">
                    {{ formatFileSize(fileItem.size) }}
                  </span>
                  <el-button
                    :icon="Delete"
                    size="small"
                    text
                    type="default"
                    aria-label="Delete file"
                    @click="handleRemoveFile(fileItem)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
          <div class="flex gap-3">
            <el-form-item label="Name" class="w-50%" prop="firstName" required>
              <el-input v-model="form.firstName" placeholder="Enter Name" />
            </el-form-item>
            <!--            <el-form-item label="Last Name" class="w-50%" prop="lastName" required> -->
            <!--              <el-input v-model="form.lastName" placeholder="Last Name" /> -->
            <!--            </el-form-item> -->
          </div>
          <el-form-item label="Email" prop="email" required>
            <el-input v-model="form.email" placeholder="Enter your email address" />
          </el-form-item>
          <el-form-item label="Your Company Name">
            <el-input v-model="form.company" placeholder="Enter your company name" />
          </el-form-item>
          <!-- Send 按钮固定在卡片右下角 -->
          <div class="send-button-container">
            <el-button type="primary" size="large" class="send-button" @click="handleSubmit">
              Send
            </el-button>
          </div>
        </el-form>
      </div>
      <!-- 右侧信息 -->
      <div class="flex flex-col gap-3 w-600px">
        <div class="p-6 rounded-lg bg-white shadow">
          <h3 class="text-lg font-semibold mb-4">
            Supplier list
          </h3>

          <!-- 已添加的供应商列表 -->
          <div v-if="addedSuppliers.length > 0" class="mb-4">
            <div
              v-for="supplier in addedSuppliers"
              :key="supplier.supplier_name"
              class="supplier-item mb-3 p-3 flex min-w-0 items-center justify-between"
            >
              <div class="flex flex-1 items-center">
                <!-- 供应商Logo -->
                <div class="mr-3 flex-shrink-0">
                  <div class="border border-gray-200 rounded-lg bg-gray-50 flex h-12 w-12 items-center justify-center">
                    <img
                      v-if="supplier.supplier_logo"
                      :src="supplier.supplier_logo"
                      :alt="supplier.supplier_name"
                      class="max-h-full max-w-full object-contain"
                    >
                    <span v-else class="text-xs text-gray-400">Logo</span>
                  </div>
                </div>

                <!-- 供应商信息 -->
                <div class="flex-1 min-w-0">
                  <div class="text-gray-900 font-medium max-w-88 truncate" :title="supplier.supplier_name">
                    {{ supplier.supplier_name }}
                  </div>
                  <div class="text-sm text-gray-500 mt-1 flex items-center">
                    <span class="text-gray-400 mr-2">Location</span>
                    <span class="max-w-40 truncate" :title="supplier.location || 'Location not specified'">{{ supplier.location || 'Location not specified' }}</span>
                  </div>
                </div>

                <!-- 认证标签 -->
                <div v-if="supplier.vetted" class="ml-2 flex-shrink-0">
                  <img src="~/assets/images/esic-tag.png" alt="ESIC Vetted" class="h-6 w-auto">
                </div>
              </div>

              <!-- 删除按钮 -->
              <el-button
                :icon="Delete"
                size="small"
                text
                class="ml-2"
                @click="removeSupplier(supplier)"
              />
            </div>
          </div>

          <!-- 添加供应商搜索 -->
          <div class="text-sm font-medium mb-2">
            Add supplier
          </div>
          <el-select
            v-model="additionalData.supplierSearch"
            placeholder="Search for suppliers"
            filterable
            remote
            reserve-keyword
            :remote-method="searchSuppliers"
            :loading="isSearching"
            class="w-full"
            @change="handleSupplierSelect"
          >
            <el-option
              v-for="supplier in searchResults"
              :key="supplier.supplier_name"
              :label="supplier.supplier_name"
              :value="supplier.supplier_name"
            />
          </el-select>
        </div>
        <div class="p-6 rounded-lg bg-white flex-none shadow">
          <h3 class="text-lg font-semibold mb-4">
            More details
          </h3>
          <el-form label-position="top" size="large">
            <el-form-item label="Select info">
              <el-input v-model="additionalData.selectInfo" placeholder="Search by category, supplier, or keyword" />
            </el-form-item>
            <el-form-item label="Categories">
              <el-cascader
                v-model="additionalData.cids"
                :options="cacheStore.getCategoryTree"
                :filter-method="categoryFilterMethod"
                filterable
                :props="{
                  multiple: true,
                  value: 'id',
                  label: 'name',
                }"
                class="w-full"
                collapse-tags
                collapse-tags-tooltip
                :show-all-levels="false"
                :max-collapse-tags="3"
                placeholder="Search by categories"
                clearable
              />
            </el-form-item>
            <el-form-item label="Tags">
              <el-select
                v-model="additionalData.tags"
                multiple
                filterable
                remote
                reserve-keyword
                :remote-method="searchTags"
                :loading="isTagSearching"
                default-first-option
                class="tag-select w-full"
                placeholder="Search by tags"
              >
                <el-option
                  v-for="tag in tagSearchResults"
                  :key="tag"
                  :label="tag"
                  :value="tag"
                >
                  <span class="tag-option-text" :title="tag">{{ tag }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bg-#F8F8F8 {
  background-color: #f8f8f8;
}

:deep(.el-upload-dragger) {
  background: #f2f4f9;
}
:deep(.el-upload-dragger.is-dragover) {
  background: #dbeaf1 !important;
}

.success-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 供应商列表样式 */
.supplier-item {
  transition: all 0.2s ease;
}

.supplier-item:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

/* 文本截断样式 */
.max-w-48 {
  max-width: 12rem; /* 192px */
}

.max-w-40 {
  max-width: 10rem; /* 160px */
}

/* 确保截断效果 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Tag 选择器样式 */
.tag-select {
  width: 100%;
}

/* 已选择的标签样式 */
:deep(.tag-select .el-select__tags .el-tag) {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.tag-select .el-select__tags .el-tag .el-tag__content) {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 下拉选项样式 */
:deep(.el-select-dropdown .el-select-dropdown__item) {
  max-width: 550px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 12px;
}

/* 选项文本样式 */
.tag-option-text {
  display: inline-block;
  max-width: 552px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

/* 限制下拉框的最大宽度 */
:deep(.el-select-dropdown) {
  max-width: 600px;
  min-width: 200px;
}

/* Send 按钮固定在卡片右下角 */
.send-button-container {
  position: absolute;
  bottom: 32px;
  right: 32px;
  z-index: 10;
}

.send-button {
  width: 120px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.send-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* 表单底部留出空间给按钮 */
.form-with-fixed-button {
  padding-bottom: 80px;
}
</style>
