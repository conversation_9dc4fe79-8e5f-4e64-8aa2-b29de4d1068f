<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { UserApi } from '~/composables/api/types'
import { Loading } from '@element-plus/icons-vue'
import ForgetPass from '~/components/common/ForgetPass.vue'
import { useCurrentUser, useUpdateUserProfile } from '~/composables/api/modules/user'
import { useUserStore } from '~/composables/user'
import emitter from '~/utils/emitter'

// 注意：根据 /admin-api/system/user/profile/ 接口文档，
// 返回的数据中可能不包含 companyName 字段
// 如果需要公司名称信息，可能需要从其他接口获取

// 页面标题
definePageMeta({
  title: 'Account Settings',
})

// 用户存储
const userStore = useUserStore()
const { user, loadUserInfo } = useCurrentUser()

// 弹窗控制
const editDialogVisible = ref(false)
const formRef = ref<FormInstance>()
const loading = ref(false)
const pageLoading = ref(false)

// 表单数据
const form = ref({
  nickname: '',
  companyName: '',
  mobile: '',
  email: '',
})

// 表单验证规则
const rules: FormRules = {
  nickname: [
    { required: true, message: 'Name is required', trigger: 'blur' },
    { min: 2, max: 50, message: 'Name length should be 2-50 characters', trigger: 'blur' },
  ],
  companyName: [
    { max: 100, message: 'Company name should not exceed 100 characters', trigger: 'blur' },
  ],
  mobile: [
    { pattern: /^[\d\s\-+()]+$/, message: 'Please enter a valid phone number', trigger: 'blur' },
  ],
}

// 计算属性：获取用户信息
const userInfo = computed(() => {
  return user.value
})

// 显示编辑弹窗
function showEditDialog() {
  if (userInfo.value) {
    form.value = {
      nickname: userInfo.value.nickname || '',
      companyName: userInfo.value.companyName || '', // 注意：此字段可能在接口中不存在
      mobile: userInfo.value.mobile || '',
      email: userInfo.value.email || '',
    }
  }
  editDialogVisible.value = true
}

// 取消编辑
function cancelEdit() {
  editDialogVisible.value = false
  formRef.value?.resetFields()
}

// 更新用户信息
async function updateUserInfo() {
  if (!formRef.value)
    return

  try {
    const valid = await formRef.value.validate()
    if (!valid)
      return

    loading.value = true

    // 准备更新参数（注意：接口不支持 companyName 字段）
    const updateParams: UserApi.UpdateUserProfileParams = {
      nickname: form.value.nickname,
      mobile: form.value.mobile,
      companyName: form.value.companyName,
      // companyName 字段不被接口支持，因此不发送
    }

    // 调用更新用户信息的API
    const response = await useUpdateUserProfile(updateParams)

    if (response.code === 0) {
      ElMessage.success('Profile updated successfully')
      editDialogVisible.value = false

      // 更新本地用户信息
      if (userStore.userInfo) {
        userStore.userInfo.nickname = form.value.nickname
        userStore.userInfo.mobile = form.value.mobile
        userStore.userInfo.companyName = form.value.companyName
        // 注意：不更新 companyName，因为接口不支持此字段
      }

      // 重新加载用户信息
      await loadUserInfo()
    }
    else {
      ElMessage.error(response.msg || 'Failed to update profile')
    }
  }
  catch (error: any) {
    console.error('Update user info error:', error)
    ElMessage.error(error.message || 'Failed to update profile')
  }
  finally {
    loading.value = false
  }
}
function switchToForgetPass() {
  emitter.emit('open-forget-pass' , 'account')
}

// 页面加载时获取用户信息
onMounted(async () => {
  pageLoading.value = true
  try {
    await loadUserInfo()
  }
  catch (error) {
    console.error('Failed to load user info:', error)
  }
  finally {
    pageLoading.value = false
  }
})
</script>

<template>
  <div class="py-8 bg-#F5F5F5">
    <div class="ml-10% px-6 max-w-4xl">
      <!-- 页面标题 -->
      <h1 class="text-2xl text-gray-900 font-semibold mb-8">
        Account Settings
      </h1>

      <!-- 加载状态 -->
      <div v-if="pageLoading" class="py-12 flex items-center justify-center">
        <el-icon class="is-loading text-primary text-2xl">
          <Loading />
        </el-icon>
        <span class="text-gray-600 ml-2">Loading user information...</span>
      </div>

      <!-- 基本信息卡片 -->
      <div v-if="!pageLoading" class="border-b border-gray-200 bg-white">
        <div class="px-6 py-4 flex items-center justify-between">
          <h2 class="text-lg text-gray-900 font-medium">
            General
          </h2>
          <el-button type="info" link @click="showEditDialog">
            Edit
          </el-button>
        </div>

        <div class="px-6 py-6 space-y-6">
          <!-- 姓名 -->
          <div>
            <label class="text-sm text-gray-500 font-medium mb-2 block">Name</label>
            <div class="text-gray-900">
              {{ userInfo?.nickname || '-' }}
            </div>
          </div>

          <!-- 公司名称 -->
          <div>
            <label class="text-sm text-gray-500 font-medium mb-2 block">Company Name</label>
            <div class="text-gray-900">
              {{ userInfo?.companyName || '-' }}
            </div>
          </div>

          <!-- 电话号码 -->
          <div>
            <label class="text-sm text-gray-500 font-medium mb-2 block">Phone Number</label>
            <div class="text-gray-900">
              {{ userInfo?.mobile || '-' }}
            </div>
          </div>

          <!-- 邮箱 -->
          <div>
            <label class="text-sm text-gray-700 font-medium mb-2 block">Email</label>
            <div class="text-gray-900">
              {{ userInfo?.email || '-' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 安全设置卡片 -->
      <div v-if="!pageLoading" class="bg-white">
        <div class="px-6 py-4">
          <h2 class="text-lg text-gray-900 font-medium">
            Security
          </h2>
        </div>

        <div class="px-6 py-6">
          <div class="items-center justify-between">
            <div>
              <label class="text-sm text-gray-700 font-medium mb-1 block">Password</label>
            </div>
            <div>
              <el-button type="info" link @click="switchToForgetPass">
                Change password
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="General"
      width="900px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        class="space-y-4"
      >
        <!-- 姓名 -->
        <el-form-item label="Name" prop="nickname">
          <el-input
            v-model="form.nickname"
            placeholder="Enter your name"
            size="large"
          />
        </el-form-item>

        <!-- 公司名称 -->
        <el-form-item label="Company Name" prop="companyName">
          <el-input
            v-model="form.companyName"
            size="large"
          />

        </el-form-item>

        <!-- 电话号码 -->
        <el-form-item label="Phone Number" prop="mobile">
          <el-input
            v-model="form.mobile"
            placeholder="Input"
            size="large"
          />
        </el-form-item>

        <!-- 邮箱（不可编辑） -->
        <el-form-item label="Email">
          <el-input
            v-model="form.email"
            disabled
            size="large"
          />
          <div class="text-sm text-gray-500 mt-1">
            Email cannot be edited.
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button type="info" link @click="cancelEdit">
            Cancel
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            class="w-280px"
            @click="updateUserInfo"
          >
            Update
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
:deep(.el-dialog__header) {
  padding: 20px 24px 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f9fafb;
  border-color: #e5e7eb;
}
</style>
