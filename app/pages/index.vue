<script setup lang="ts">
// 使用new URL导入图片
import homeBgUrl from '../assets/images/home-bg.png'
import { useSearchFlow } from '~/composables/api/modules/search'

defineOptions({
  name: 'IndexPage',
})
definePageMeta({
  layout: 'home',
})
// 使用全局搜索状态
const useSearch = useSearchFlow()
const router = useRouter()
const localePath = useLocalePath()

const { t } = useI18n() // 引入 useI18n
useHead({
  title: () => 'ESIC Select', // 可以用于设置页面标题
})
</script>

<template>
  <div
    :style="`background-image: url(${homeBgUrl});
       background-size: cover; background-position: center;
       min-h-screen
       `"
  >
    <NavBar />
    <div
      class="px-5 flex w-full justify-center"
      style="height: calc(100vh - 71px)"
    >
      <div class="mt-189px max-w-1200px w-full">
        <div class="text-center flex flex-col items-center">
          <h1 class="font-poppinsBold text-5xl text-gray-700 font-bold mb-4">
            {{ t('home.title') }}
          </h1>
          <p class="font-poppinsRegular text-xl text-gray-700 tracking-0.16em mb-10">
            {{ t('home.subtitle') }}
          </p>

          <div class="flex flex-wrap max-w-800px w-full justify-center justify-center">
            <div class="homeInput mb-10 w-full">
              <el-input
                v-model="useSearch.searchText"
                :placeholder="t('home.search_placeholder')"
                class="b-rd-8px b-r-none h-56px w-full"
                @keyup.enter="() => { router.push({ path: localePath('search'), query: { keyword: useSearch.searchText } }) }"
              >
                <template #append>
                  <el-button
                    text
                    class="text-5 border-none bg-transparent !font-bold" style="height: 56px;border-radius: 0 8px 8px 0;"
                    @click="() => { router.push({ path: localePath('search'), query: { keyword: useSearch.searchText } }) }"
                  >
                    {{ t('home.search_button') }}
                  </el-button>
                </template>
              </el-input>
            </div>

            <div class="text-base text-gray-700 b-rd-full bg-#E2ECF1 flex items-center justify-center" style="background-color: #E2ECF1;">
              <Categories />
              <div class="text-gray px-6">
                |
              </div>
              <VettedSupplier />
              <div class="text-gray px-6">
                |
              </div>
              <Buyers />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-input-group__append) {
  font-size: 20px;
  font-weight: bold;
  background-color: #ffffff;
  color: #2c3e50;
  border-radius: 0 8px 8px 0;
  border-left: none;
}

.homeInput {
  :deep(.el-input__wrapper) {
    border-radius: 8px 0 0 8px;
    padding-right: 0;
  }
}

.stats-item {
  position: relative;
  overflow: hidden;
}
</style>
