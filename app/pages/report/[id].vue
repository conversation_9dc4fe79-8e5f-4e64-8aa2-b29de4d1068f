<script setup lang="ts">
import { isNumber } from 'lodash'
import { useCompanyInfo } from '~/composables/api/modules/company'

// 获取公司信息API
const {
  capabilities,
  loading,
  error,
  fetchCompanyDetail,
  companyInfo,
} = useCompanyInfo()

const report = ref('')
const route = useRoute()
onMounted(async () => {
  await fetchCompanyDetail(route.params?.id)
  try {
    report.value = JSON.parse(capabilities.value.find(item => item.categoryFirst === route.query.cateId)?.aiReport)
  }
  catch (error) {
  }
})

const router = useRouter()
const localePath = useLocalePath()
function goBack() {
  if (window.history.length > 1) {
    router.back()
  }
  else {
    router.push(localePath('/'))
  }
}

const breadcrumbItems = computed(() => {
  const items = [
    { label: 'Home', to: { path: localePath('/') } },
  ]

  // 根据来源页面添加中间路径
  // 优先使用路由查询参数，其次使用referrer（仅在客户端）
  let fromSearch = route.query.from === 'search'
  let fromCollection = route.query.from === 'collection'
  let fromCompare = route.query.from === 'compare'

  // 在客户端检查referrer作为备选方案
  if (import.meta.client && !fromSearch && !fromCollection && !fromCompare) {
    const referrer = document.referrer
    fromSearch = referrer.includes('/search')
    fromCollection = referrer.includes('/collection')
    fromCompare = referrer.includes('/compare')
  }

  // 构建带有query参数的路由对象
  if (fromSearch) {
    // 保留搜索页面的query参数
    const searchQuery = { ...route.query }
    delete searchQuery.from // 移除from参数，避免循环
    delete searchQuery.cateId // 移除report特有的参数
    items.push({
      label: 'Search',
      to: {
        path: localePath('/search'),
        query: Object.keys(searchQuery).length > 0 ? searchQuery : undefined,
      },
    })
  }
  else if (fromCollection) {
    items.push({ label: 'Saved Suppliers', to: { path: localePath('/collection') } })
  }
  else if (fromCompare) {
    items.push({ label: 'Compare', to: { path: localePath('/compare') } })
  }
  else {
    // 默认显示Search
    items.push({ label: 'Search', to: { path: localePath('/search') } })
  }

  // 添加当前公司名称
  if (report.value.company_name?.english) {
    items.push({
      label: report.value.company_name?.english,
      to: {
        path: localePath('/supplier'),
        query: {
          companyId: companyInfo.value?.companyId,
          from: route.query.from || 'search', // 传递来源信息
        },
      },
    })
  }
  else if (report.value.company_name?.chinese) {
    items.push({
      label: report.value.company_name?.chinese,
      to: {
        path: localePath('/supplier'),
        query: {
          companyId: companyInfo.value?.companyId,
          from: route.query.from || 'search', // 传递来源信息
        },
      },
    })
  }
  items.push({ label: 'Report', to: null })

  return items
})
</script>

<template>
  <div class="min-h-screen">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container px-6 py-3 flex items-center">
      <!-- Back按钮 -->
      <el-button
        link
        type="primary"
        class="text-gray-600 mr-4 hover:text-primary"
        @click="goBack"
      >
        <Icon name="ep:arrow-left" class="mr-1" size="16" />
        Back
      </el-button>

      <!-- 分隔符 -->
      <span class="text-gray-400 mr-4">|</span>

      <!-- 面包屑路径 -->
      <el-breadcrumb separator="/" class="flex-1">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbItems"
          :key="index"
          :to="item.to"
          :class="{ 'breadcrumb-current': index === breadcrumbItems.length - 1 }"
        >
          {{ item.label }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div v-if="!loading && !error && report" class="ml-10% px-4 py-8 max-w-6xl">
      <!-- 页面标题 -->
      <div class="px-8 py-6 border-gray-200">
        <h1 class="text-3xl text-gray-900 font-bold mb-4">
          ESiC Report
        </h1>
      </div>
      <div class="rounded-lg bg-white overflow-hidden">
        <div class="px-160px py-6">
          <!-- 公司名称 -->
          <div class="mb-8">
            <h2 class="text-xl text-gray-900 font-semibold mb-2">
              {{ report.company_name?.english }}
            </h2>
            <h3 class="text-lg text-gray-700">
              {{ report.company_name?.chinese }}
            </h3>
          </div>
          <!-- ESiC recommendation -->
          <div class="mb-6">
            <h3 class="section-title">
              ESiC recommendation:
            </h3>
            <p class="text-gray-700 leading-relaxed">
              {{ report['ESiC recommendation'] }}
            </p>
          </div>

          <!-- Key strengths include -->
          <div class="mb-6">
            <h3 class="section-title">
              Key strengths include:
            </h3>
            <ul class="text-gray-700 ml-4 list-disc list-inside space-y-2">
              <li v-for="item in report['Key strengths include']" :key="item">
                {{ item }}
              </li>
            </ul>
          </div>

          <!-- Key Features and Highlights -->
          <div class="mb-6">
            <h3 class="section-title">
              Key Features and Highlights:
            </h3>
            <ul class="text-gray-700 ml-4 list-disc list-inside space-y-2">
              <li v-for="item in report['Key Features and Highlights']" :key="item">
                {{ item }}
              </li>
            </ul>
          </div>

          <!-- Basic Information -->
          <div class="mb-6 pt-6 border-gray-200">
            <h3 class="section-title">
              Basic Information:
            </h3>
            <ul class="text-gray-700 space-y-2">
              <li v-for="item in report['Basic Information']" :key="item" class="flex">
                <span class="font-semibold min-w-32">{{ item.split(': ')[0] }}:</span>
                <span class="ml-2">{{ item.split(': ')[1] }}</span>
              </li>
            </ul>
          </div>

          <!-- Capabilities Overview -->
          <div class="mb-6 pt-6 border-gray-200">
            <h3 class="section-title">
              Capabilities Overview
            </h3>
            <div v-for="(ov, ovk) in report['Capabilities Overview']" :key="ovk" class="mb-4">
              <h4 class="text-gray-800 font-semibold mb-2">
                {{ ovk }}
              </h4>
              <ul class="text-gray-700 space-y-1">
                <li v-for="(item, key) in ov" :key="key" class="ml-4">
                  <span v-if="!isNumber(key)" class="font-medium">{{ key }}:</span>

                  <ul v-if="Array.isArray(item)" class="ml-4 mt-1 list-disc list-inside space-y-1">
                    <li v-for="a in item" :key="a" class="text-sm">
                      {{ a }}
                    </li>
                  </ul>
                  <span v-else class="ml-1">{{ item }}</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Supplier Profile -->
          <div class="mb-6 pt-6 border-gray-200">
            <h3 class="section-title">
              Supplier Profile
            </h3>
            <div v-for="(item, key) in report['Supplier Profile']" :key="key" class="mb-3">
              <span v-if="!isNumber(key)" class="text-gray-800 font-semibold">{{ key }}:</span>

              <ul v-if="Array.isArray(item)" class="text-gray-700 ml-4 mt-1 list-disc list-inside space-y-1">
                <li v-for="a in item" :key="a">
                  {{ a }}
                </li>
              </ul>
              <span v-else class="text-gray-700 ml-1">{{ item }}</span>
            </div>
          </div>
          <!-- ESiC On-Site Audit Results -->
          <div class="mb-6 pt-6 border-gray-200">
            <h3 class="section-title">
              ESiC On-Site Audit Results: {{ report.esic_on_site_audit_results?.total_score }}
            </h3>
            <div class="mt-4 border border-gray-200 rounded-lg w-50% overflow-hidden">
              <table class="min-w-full divide-gray-200 divide-y">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="text-xs text-gray-500 font-medium tracking-wider px-6 py-3 text-left uppercase">
                      Category
                    </th>
                    <th class="text-xs text-gray-500 font-medium tracking-wider px-6 py-3 text-left uppercase">
                      Score
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-gray-200 divide-y">
                  <tr v-for="(item, key) in report.esic_on_site_audit_results?.category_scores" :key="key">
                    <td class="text-sm text-gray-900 font-medium px-6 py-4 whitespace-nowrap">
                      {{ key }}
                    </td>
                    <td class="text-sm text-gray-700 px-6 py-4 whitespace-nowrap">
                      {{ item }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Key Findings -->
          <div class="mb-8 pt-6 border-t border-gray-200">
            <h3 class="section-title">
              Key Findings
            </h3>
            <ol class="text-gray-700 list-decimal list-inside space-y-4">
              <li v-for="(item, key) in report.esic_on_site_audit_results?.key_findings" :key="key">
                <span class="text-gray-800 font-semibold">{{ key }}</span>
                <ul class="ml-6 mt-2 list-disc list-inside space-y-1">
                  <li v-for="a in item" :key="a" class="text-sm">
                    {{ a }}
                  </li>
                </ul>
              </li>
            </ol>
          </div>

          <!-- End of Report -->
          <div class="py-6 text-center border-t border-gray-200">
            <p class="text-gray-300">
              - End of the Report -
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  padding-left: 12px;
  position: relative;
  display: block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #4996b8;
  border-radius: 2px;
}

/* 面包屑样式 */
.breadcrumb-container {
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  color: #4996b8 !important;
  font-weight: 500;
}

:deep(.breadcrumb-current) {
  color: #374151 !important;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #374151 !important;
  font-weight: 500;
}

:deep(.el-breadcrumb__item:not(:last-child) .el-breadcrumb__inner:hover) {
  color: #374151 !important;
}
.supplier-card {
  padding-right: calc((80% - 2rem) / 3 - 336px + 10%);
}
</style>
