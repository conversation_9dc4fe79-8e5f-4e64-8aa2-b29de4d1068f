<script setup lang="ts">
import * as echarts from 'echarts'
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  option: {
    type: Object,
    required: true,
  },
  height: {
    type: String,
    default: '300px',
  },
  width: {
    type: String,
    default: '100%',
  },
})

const chartContainer = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
let resizeObserver: ResizeObserver | null = null
let resizeTimer: NodeJS.Timeout | null = null

// 防抖处理的 resize 函数
function debouncedResize() {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }, 100)
}

async function initChart() {
  await nextTick()

  if (!chartContainer.value) return

  // 等待容器渲染完成
  await new Promise(resolve => setTimeout(resolve, 50))

  const containerWidth = chartContainer.value.clientWidth
  const containerHeight = chartContainer.value.clientHeight

  if (containerWidth > 0 && containerHeight > 0) {
    // 如果已经有实例，先销毁
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }

    try {
      chartInstance = echarts.init(chartContainer.value)
      chartInstance.setOption(props.option)

      // 初始化后立即调用一次 resize 确保尺寸正确
      setTimeout(() => {
        if (chartInstance) {
          chartInstance.resize()
        }
      }, 100)
    } catch (error) {
      console.error('图表初始化失败:', error)
    }
  } else {
    // 如果容器尺寸为0，延迟初始化
    setTimeout(initChart, 200)
  }
}

onMounted(() => {
  initChart()

  // 监听窗口大小变化
  window.addEventListener('resize', debouncedResize)

  // 使用 ResizeObserver 监听容器尺寸变化
  if (chartContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === chartContainer.value) {
          debouncedResize()
        }
      }
    })
    resizeObserver.observe(chartContainer.value)
  }
})

onUnmounted(() => {
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }

  // 移除事件监听
  window.removeEventListener('resize', debouncedResize)

  // 断开 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }

  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

watch(() => props.option, (newOption) => {
  if (chartInstance && newOption) {
    try {
      chartInstance.setOption(newOption, true)
      // 设置选项后调用 resize 确保布局正确
      setTimeout(() => {
        if (chartInstance) {
          chartInstance.resize()
        }
      }, 50)
    } catch (error) {
      console.error('图表选项更新失败:', error)
    }
  }
}, { deep: true })

// 暴露方法供父组件调用
defineExpose({
  resize: () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  },
  getInstance: () => chartInstance
})
</script>

<template>
  <div ref="chartContainer" :style="{ width, height }" />
</template>

<style scoped>
div {
  min-width: 200px; /* 确保最小宽度为200px */
}
</style>
