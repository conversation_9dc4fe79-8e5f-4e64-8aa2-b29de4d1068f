<script setup lang="ts">
import { useCopyToClipboard } from '~/composables/useCopyToClipboard'

// 引入必要的响应式API
const props = defineProps({
  // 自定义成功提示消息
  successMessage: {
    type: String,
    default: 'Copied successfully',
  },
  // 自定义失败提示消息
  errorMessage: {
    type: String,
    default: 'Failed to copy',
  },
  // 复制按钮文字
  buttonText: {
    type: String,
    default: 'Copy',
  },
})

// 使用模板引用替代选择器查询
const contentRef = ref(null)

// 使用兼容性更好的复制功能
const { copy, copying } = useCopyToClipboard()

// 复制内容到剪贴板
async function copyToClipboard() {
  if (contentRef.value) {
    const text = contentRef.value.textContent || ''
    if (text.trim()) {
      await copy(text, props.successMessage, props.errorMessage)
    }
  }
}
</script>

<template>
  <div class="copy-container">
    <div ref="contentRef" class="copy-content">
      <slot />
    </div>
    <el-button
      class="copy-button"
      size="small"
      link
      type="info"
      :loading="copying"
      @click="copyToClipboard"
    >
      {{ buttonText }}
    </el-button>
  </div>
</template>

<style scoped>
.copy-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copy-button {
  flex-shrink: 0;
}
</style>
