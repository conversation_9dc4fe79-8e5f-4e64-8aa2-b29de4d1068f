<script setup lang="ts">
// 使用Nuxt提供的导航功能
const router = useRouter()
const localePath = useLocalePath()

// 跳转到首页
function goToHome() {
  router.push(localePath('index'))
}
</script>

<template>
  <div class="logo-container" @click="goToHome">
    <img src="~/assets/images/logo.png" alt="Logo" class="logo-image">
    <span class="px-3">x</span>

    <span class="logo-text">Bole</span>
  </div>
</template>

<style scoped>
.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.3s;
}

.logo-container:hover {
  opacity: 0.8;
}

.logo-image {
  height: 40px;
  width: auto;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #314564;
}
</style>
