<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ArrowLeft, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, reactive, ref } from 'vue'
import {useResetPassword, useSendMailCode, useValidateMailCode} from '~/composables/api/modules'
import emitter from '~/utils/emitter'

// 弹窗可见性控制
const dialogVisible = ref(false)
// 当前步骤：1-输入邮箱，2-验证码验证，3-重置密码
const currentStep = ref(1)

// 表单引用
const formRef = ref<FormInstance>()
// 加载状态
const loading = ref(false)
// 验证码重发倒计时
const resendCountdown = ref(0)
// 倒计时定时器
const countdownTimer = ref<number | null>(null)

// 邮箱表单
const emailForm = ref({
  email: '',
})

// 验证码表单
const verifyForm = ref({
  code: '',
})

// 密码表单
const passwordForm = ref({
  password: '',
  confirmPassword: '',
})

// 表单验证规则
const emailRules = reactive<FormRules>({
  email: [
    { required: true, message: 'Enter your email', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email', trigger: 'blur' },
  ],
})

const codeRules = reactive<FormRules>({
  code: [
    { required: true, message: 'Enter verification code', trigger: 'blur' },
  ],
})

const passwordRules = reactive<FormRules>({
  password: [
    { required: true, message: 'Enter new password', trigger: 'blur' },
    { min: 8, message: 'At least 8 characters', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: 'Confirm your password', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.password)
          callback(new Error('Passwords do not match'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
})

// 显示弹窗
function showForgetPassDialog() {
  resetForms()
  dialogVisible.value = true
  currentStep.value = 1
  stopCountdown() // 重置倒计时
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false
  resetForms()
  stopCountdown() // 重置倒计时
}

// 重置所有表单
function resetForms() {
  emailForm.value.email = ''
  verifyForm.value.code = ''
  passwordForm.value.password = ''
  passwordForm.value.confirmPassword = ''

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 返回上一步
function goBack() {
  if (currentStep.value > 1){
    currentStep.value--
  }
  else{
    closeDialog()
    emitter.emit('open-login')
  }

}

// 发送邮箱验证码
async function sendVerificationEmail() {
  if (!formRef.value)
    return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 调用发送验证码接口
        useSendMailCode(emailForm.value.email, 5).then(() => {
          ElMessage.success('The verification code has been sent, please check your mailbox.')
          currentStep.value = 2
          startCountdown() // 开始倒计时
          verifyForm.value.code = ''
        })
        // 跳转到验证码步骤
      }
      catch (error) {
        console.error('Failed to send verification code:', error)
      }
      finally {
        loading.value = false
      }
    }
  })
}

// 验证验证码
async function verifyCode() {
  if (!formRef.value)
    return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 调用验证码验证接口
        useValidateMailCode(emailForm.value.email, verifyForm.value.code).then(() => {
          currentStep.value = 3
        })
        // 跳转到重置密码步骤
      }
      catch (error) {
      }
      finally {
        loading.value = false
      }
    }
  })
}

// 重新发送验证码
async function resendCode() {
  try {
    loading.value = true

    // 调用重新发送验证码接口
    useSendMailCode(emailForm.value.email, 5).then(() => {
      ElMessage.success('The verification code has been resent, please check your mailbox.')
      startCountdown() // 开始倒计时
    })
  }
  catch (error) {
    console.error('Failed to resend code:', error)
  }
  finally {
    loading.value = false
  }
}

// 重置密码
async function resetPassword() {
  if (!formRef.value)
    return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 调用重置密码接口
        await useResetPassword(
            emailForm.value.email,
            passwordForm.value.password,
        )

        ElMessage.success('Password reset successfully')
        closeDialog()

        // 可选：打开登录弹窗
        emitter.emit('open-login')
      }
      catch (error) {
        console.error('Failed to reset password:', error)
        ElMessage.error('Failed to reset password, please try again')
      }
      finally {
        loading.value = false
      }
    }
  })
}

// 开始倒计时
function startCountdown() {
  stopCountdown() // 确保先清除之前的定时器
  resendCountdown.value = 60

  countdownTimer.value = setInterval(() => {
    if (resendCountdown.value > 0) {
      resendCountdown.value--
    }
    else {
      stopCountdown()
    }
  }, 1000)
}

// 停止倒计时
function stopCountdown() {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  resendCountdown.value = 0
}
const showBackButton = ref(false)

// 监听打开忘记密码对话框事件
onMounted(() => {
  emitter.on('open-forget-pass', (whereFrom = 'login') => {
    if (whereFrom === 'login') {
      showBackButton.value = true
    }
    showForgetPassDialog()
  })
})

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  emitter.off('open-forget-pass')
  stopCountdown() // 清理定时器
})

defineExpose({
  showForgetPassDialog,
})
</script>

<template>
  <div class="forget-pass-container">
    <el-dialog
      v-model="dialogVisible"
      :z-index="1001"
      width="440px"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      custom-class="forget-pass-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <div v-if="currentStep === 1 || currentStep === 3">
            Reset your password
          </div>
          <div v-else-if="currentStep === 2">
            Verify your email
          </div>
          <el-icon class="close-icon" @click="closeDialog">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="dialog-content">
        <!-- 步骤1：输入邮箱 -->
        <template v-if="currentStep === 1">
          <p class="instruction-text">
            Enter your email address that you use with your account to continue.
          </p>

          <el-form
            ref="formRef"
            :model="emailForm"
            :rules="emailRules"
            label-position="top"
            :hide-required-asterisk="true"
          >
            <el-form-item prop="email">
              <el-input
                v-model="emailForm.email"
                placeholder="Input your email"
                class="form-input"
              />
            </el-form-item>

            <el-button
              type="primary"
              class="action-btn"
              :loading="loading"
              @click="sendVerificationEmail"
            >
              Continue
            </el-button>
          </el-form>
        </template>

        <!-- 步骤2：验证邮箱 -->
        <template v-else-if="currentStep === 2">
          <p class="instruction-text">
            A verification code has been sent to your email<br>
            <span class="email-display">{{ emailForm.email }}</span>
          </p>

          <el-form
            ref="formRef"
            :model="verifyForm"
            :rules="codeRules"
            label-position="top"
            :hide-required-asterisk="true"
          >
            <el-form-item prop="code">
              <div class="code-input-container w-full">
                <el-input
                  v-model="verifyForm.code"
                  placeholder="Enter code"
                  class="form-input"
                />
              </div>
            </el-form-item>
            <div class="mt--3 flex justify-end">
              <el-button
                type="text"
                class="{ resendCountdown === 0 || !loading } 'resend-btn':"
                :disabled="loading || resendCountdown > 0"
                @click="resendCode"
              >
                {{ resendCountdown > 0 ? `Resend (${resendCountdown}s)` : 'Resend' }}
              </el-button>
            </div>

            <el-button
              type="primary"
              class="action-btn"
              :loading="loading"
              @click="verifyCode"
            >
              Verify
            </el-button>
          </el-form>
        </template>

        <!-- 步骤3：设置新密码 -->
        <template v-else-if="currentStep === 3">
          <p class="instruction-text">
            Enter your email address that you use with your account to continue.
          </p>

          <el-form
            ref="formRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-position="top"
            :hide-required-asterisk="true"
          >
            <el-form-item label="New password" prop="password">
              <el-input
                v-model="passwordForm.password"
                type="password"
                placeholder="At least 8 characters"
                class="form-input"
                show-password
              />
            </el-form-item>

            <el-form-item label="Confirm password" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="Input"
                class="form-input"
                show-password
              />
            </el-form-item>

            <el-button
              type="primary"
              class="action-btn"
              :loading="loading"
              @click="resetPassword"
            >
              Continue
            </el-button>
          </el-form>
        </template>
      </div>

      <div v-if="currentStep < 3" class="dialog-footer">
        <div class="back-section !justify-start">
          <el-button
            type="text"
            v-if="showBackButton"
            class="back-btn"
            :icon="ArrowLeft"
            @click="goBack"
          >
            Back
          </el-button>
        </div>
      </div>

      <div v-else class="dialog-footer">
        <div class="divider" />
        <div class="login-section">
          <p class="login-hint">
            Already have an account?
            <el-button
              type="text"
              class="login-btn color-#409eff"
              @click="closeDialog(); emitter.emit('open-login')"
            >
              Log in
            </el-button>
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.forget-pass-dialog) {
  border-radius: 16px;
  padding: 0;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 0;
}

:deep(.el-dialog__body) {
  padding: 0 24px 24px;
}

.dialog-header {
  display: flex;
  font-size: 24px;
  font-weight: 500;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
}

.instruction-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 24px;
}

.email-display {
  font-weight: 500;
  color: #303133;
}

.form-input {
  border-radius: 8px;
}

.code-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resend-btn {
  font-size: 14px;
  color: #409eff;
  white-space: nowrap;
}

.action-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  margin-top: 8px;
}

.dialog-footer {
  margin-top: 36px;
}

.back-section,
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn,
.login-btn {
  font-size: 14px;
  color: #383838;
}

.login-hint {
  text-align: center;
  font-size: 14px;
  color: #606266;
}
.dialog-content {
  min-height: 483px;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 20px 0;
}
</style>
