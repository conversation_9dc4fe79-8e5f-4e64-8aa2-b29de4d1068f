<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { Check, Close, WarningFilled } from '@element-plus/icons-vue'
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { useRegister, useSendMailCode, useValidateMailCode } from '~/composables/api/modules'
import emitter from '~/utils/emitter'

interface PermissionResponse {
  code: number
  data: {
    user: {
      id: number
      nickname: string
      avatar: string
      deptId: number
    }
    roles: string[]
    permissions: string[]
  }
  msg: string
}

// 添加注册/登录响应类型定义
interface AuthResponse {
  code: number
  data: {
    accessToken?: string
    refreshToken?: string
    userId?: number
    expiresTime?: number
  }
  msg: string
}

const dialogVisible = ref(false)
const form = ref({
  email: '',
  code: '',
  name: '',
  password: '',
  confirmPassword: '',
})

const formRef = ref<FormInstance>()
const passwordFocused = ref(false)

// 密码强度检测
const passwordStrength = reactive({
  hasMinLength: false,
  hasLowerCase: false,
  hasUpperCase: false,
  hasNumber: false,
  hasSpecialChar: false,
})

// 监听密码变化，实时更新密码强度
watch(() => form.value.password, (newPassword) => {
  passwordStrength.hasMinLength = newPassword.length >= 8
  passwordStrength.hasLowerCase = /[a-z]/.test(newPassword)
  passwordStrength.hasUpperCase = /[A-Z]/.test(newPassword)
  passwordStrength.hasNumber = /\d/.test(newPassword)
  passwordStrength.hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(newPassword)
})

// 表单验证规则
const rules = reactive({
  email: [
    { required: true, message: 'Enter your email', trigger: 'blur' },
    { type: 'email' as const, message: 'Please enter a valid email', trigger: 'blur' },
  ],
  code: [{
    required: true,
    message: 'Enter your code',
    trigger: 'blur',
  }],
  name: [
    { required: true, message: 'Enter your name', trigger: 'blur' },
  ],
  password: [
    { required: true, message: 'Enter your password', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (passwordStrength.hasMinLength && passwordStrength.hasLowerCase && passwordStrength.hasUpperCase && passwordStrength.hasNumber && passwordStrength.hasSpecialChar) {
          callback()
        }
        else {
          callback(new Error('Please try a stronger password.'))
        }
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: 'Confirm your password', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== form.value.password) {
          callback(new Error('Those passwords didn\'t match. Try again.'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
})

const registerStep = ref(0)
const codeTime = ref(0)
const isSending = ref(false)
const isSubmitting = ref(false)
let timer: any = null

// 倒计时函数
function startCountdown() {
  codeTime.value = 60
  isSending.value = true

  clearInterval(timer)
  timer = setInterval(() => {
    if (codeTime.value > 0) {
      codeTime.value--
    }
    else {
      clearInterval(timer)
      isSending.value = false
    }
  }, 1000)
}

// 重置倒计时
function resetCountdown() {
  clearInterval(timer)
  codeTime.value = 0
  isSending.value = false
}

// 重置所有状态到初始状态
function resetForm() {
  form.value = {
    email: '',
    code: '',
    name: '',
    password: '',
    confirmPassword: '',
  }
  registerStep.value = 0
  resetCountdown()
  isSubmitting.value = false
  passwordFocused.value = false
  // 重置密码强度检测
  Object.keys(passwordStrength).forEach((key) => {
    passwordStrength[key as keyof typeof passwordStrength] = false
  })

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

function showRegisterDialog() {
  resetForm()
  dialogVisible.value = true
}

function handleContinue() {
  if (formRef.value) {
    formRef.value.validate((valid) => {
      if (valid) {
        isSending.value = true
        useSendMailCode(form.value.email, 5).then(() => {
          ElMessage.success('The verification code has been sent, please check your mailbox.')
          registerStep.value = 1
          startCountdown()
        }).catch(() => {
          isSending.value = false
        })
      }
    })
  }
}

function resendCode() {
  if (isSending.value || codeTime.value > 0)
    return

  isSending.value = true
  useSendMailCode(form.value.email, 5).then(() => {
    ElMessage.success('The verification code has been sent, please check your mailbox.')
    startCountdown()
  }).catch(() => {
    isSending.value = false
  })
}

function handleVerify() {
  if (formRef.value) {
    formRef.value.validate((valid) => {
      if (valid) {
        useValidateMailCode(form.value.email, form.value.code).then((res) => {
          if (res.code === 0) {
            ElMessage.success('Verification code verified successfully')
            registerStep.value = 2
          }
        }).catch((_error) => {
          // 错误已在UI中处理
        })
      }
    })
  }
}
const userStore = useUserStore()
const accessToken = useCookie('accessToken', {
  maxAge: 60 * 60 * 24 * 7, // 7天过期
  path: '/',
  sameSite: 'lax',
})
const refreshToken = useCookie('refreshToken', {
  maxAge: 60 * 60 * 24 * 30, // 30天过期
  path: '/',
  sameSite: 'lax',
})

function handleSubmit() {
  if (formRef.value) {
    formRef.value.validate((valid) => {
      if (valid) {
        isSubmitting.value = true

        useRegister(form.value.email, form.value.password, form.value.name)
          .then(async (res) => {
            if (res.code === 0) {
              ElMessage.success('Registration successful')
              dialogVisible.value = false

              // 注册成功后走登录流程
              // 检查响应数据是否包含token
              if (res.data && res.data.accessToken) {
                userStore.setToken(res.data.accessToken)

                // 设置cookie - 确保cookie名称与后端期望一致
                accessToken.value = res.data.accessToken

                if (res.data.refreshToken) {
                  refreshToken.value = res.data.refreshToken
                }

                // 延迟一下再请求用户信息，确保cookie已被浏览器处理
                setTimeout(async () => {
                  try {
                    const permissionRes = await $fetch<PermissionResponse>('/api/auth/get-permission-info')

                    if (permissionRes.code === 0) {
                      userStore.setPermissionInfo(permissionRes.data)
                      ElMessage.success(`Welcome，${permissionRes.data.user.nickname}`)
                    }
                    else {
                      ElMessage.error(permissionRes.msg || '获取用户信息失败')
                    }
                  }
                  catch (error) {
                    console.error('获取用户信息失败:', error)
                    ElMessage.error('登录成功但获取用户信息失败')
                  }
                }, 100) // 短暂延迟确保cookie已设置
              }
              else {
                ElMessage.warning('登录成功但未获取到令牌')
              }
            }
            else {
              ElMessage.error(res.msg || 'Registration failed')
            }
            isSubmitting.value = false
          })
          .catch((error) => {
            ElMessage.error(error.message || 'Registration failed, please try again')
            isSubmitting.value = false
          })
      }
    })
  }
}

// 切换到登录页面
function switchToLogin() {
  dialogVisible.value = false
  emitter.emit('open-login')
}

// 监听对话框关闭，重置表单
watch(() => dialogVisible.value, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 监听事件
onMounted(() => {
  // 监听打开注册对话框事件
  emitter.on('open-register', () => {
    resetForm()
    dialogVisible.value = true
  })
})

// 组件销毁前移除事件监听和清除定时器
onBeforeUnmount(() => {
  emitter.off('open-register')
  resetCountdown()
})

// 导出方法供父组件调用
defineExpose({
  showRegisterDialog,
})
</script>

<template>
  <div class="container">
    <el-dialog
      v-model="dialogVisible"
      width="440px"
      :z-index="1001"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      custom-class="signup-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <div>Sign up to ESiC Bole</div>
          <el-icon class="close-icon" @click="dialogVisible = false">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="dialog-content">
        <p class="welcome-text">
          Welcome, create an account to access 900+ suppliers, professional outsourcing Services, and more.
        </p>

        <div class="form-section">
          <!-- Step 0 & 1: Email verification -->
          <template v-if="registerStep < 2">
            <p class="section-title">
              Verify your mail
            </p>
          </template>

          <!-- Step 2: Registration form -->
          <template v-else>
            <p class="section-title verified">
              Verify your mail
              <el-icon class="verified-icon">
                <Check />
              </el-icon>
            </p>
          </template>

          <el-form ref="formRef" :model="form" :rules="rules">
            <!-- Email input -->
            <el-form-item prop="email">
              <el-input
                v-model="form.email"
                placeholder="Input your email"
                class="email-input"
                :disabled="registerStep > 0"
              />
            </el-form-item>

            <!-- Verification code (Step 1) -->
            <template v-if="registerStep === 1">
              <el-form-item prop="code">
                <el-input
                  v-model="form.code"
                  placeholder="verification code"
                  class="email-input"
                />
              </el-form-item>
              <div class="flex justify-between">
                <span>Verification code sent</span>
                <el-button
                  type="text"
                  :disabled="codeTime > 0"
                  @click="resendCode"
                >
                  {{ codeTime > 0 ? `Resend(${codeTime}s)` : 'Resend' }}
                </el-button>
              </div>
            </template>

            <!-- Full registration form (Step 2) -->
            <template v-if="registerStep === 2">
              <!-- Name input -->
              <el-form-item prop="name">
                <div class="field-label">
                  Name
                </div>
                <el-input
                  v-model="form.name"
                  placeholder="Input"
                  class="form-input"
                />
              </el-form-item>

              <!-- Password input with strength meter popover -->
              <el-form-item prop="password">
                <div class="field-label">
                  Password
                </div>
                <el-popover
                  placement="top"
                  trigger="focus"
                  :width="380"
                  :visible="passwordFocused"
                  popper-class="password-strength-popover"
                >
                  <template #reference>
                    <el-input
                      v-model="form.password"
                      type="password"
                      placeholder="At least 8 characters"
                      class="form-input"
                      show-password
                      @focus="passwordFocused = true"
                      @blur="passwordFocused = false"
                    />
                  </template>

                  <!-- Password strength indicator -->
                  <div class="password-strength-meter">
                    <div class="strength-item">
                      <el-icon v-if="passwordStrength.hasMinLength" class="icon-success" :class="{ satisfied: passwordStrength.hasMinLength }">
                        <Check />
                      </el-icon>
                      <el-icon v-else class="icon-warning">
                        <WarningFilled />
                      </el-icon>
                      <span>At least 8 characters</span>
                    </div>

                    <div class="strength-item">
                      <el-icon v-if="passwordStrength.hasLowerCase" class="icon-success" :class="{ satisfied: passwordStrength.hasLowerCase }">
                        <Check />
                      </el-icon>
                      <el-icon v-else class="icon-warning">
                        <WarningFilled />
                      </el-icon>
                      <span>Including lower case letters (a-z)</span>
                    </div>

                    <div class="strength-item">
                      <el-icon v-if="passwordStrength.hasUpperCase" class="icon-success" :class="{ satisfied: passwordStrength.hasUpperCase }">
                        <Check />
                      </el-icon>
                      <el-icon v-else class="icon-warning">
                        <WarningFilled />
                      </el-icon>
                      <span>Including upper case letters (A-Z)</span>
                    </div>

                    <div class="strength-item">
                      <el-icon v-if="passwordStrength.hasNumber" class="icon-success" :class="{ satisfied: passwordStrength.hasNumber }">
                        <Check />
                      </el-icon>
                      <el-icon v-else class="icon-warning">
                        <WarningFilled />
                      </el-icon>
                      <span>Including numbers (0-9)</span>
                    </div>

                    <div class="strength-item">
                      <el-icon v-if="passwordStrength.hasSpecialChar" class="icon-success" :class="{ satisfied: passwordStrength.hasSpecialChar }">
                        <Check />
                      </el-icon>
                      <el-icon v-else class="icon-warning">
                        <WarningFilled />
                      </el-icon>
                      <span>Including special characters (e.g. !@#$%^&*)</span>
                    </div>
                  </div>
                </el-popover>
              </el-form-item>

              <!-- Confirm password input -->
              <el-form-item prop="confirmPassword">
                <div class="field-label">
                  Confirm password
                </div>
                <el-input
                  v-model="form.confirmPassword"
                  type="password"
                  placeholder="Input"
                  class="form-input"
                  show-password
                />
              </el-form-item>

              <!-- Terms of service text -->
            </template>
          </el-form>

          <!-- Button for Step 0: Continue to verification -->
          <el-button v-if="registerStep === 0" type="primary" class="continue-btn" :loading="isSending" @click="handleContinue">
            Continue
          </el-button>

          <!-- Button for Step 1: Verify code -->
          <el-button v-if="registerStep === 1" type="primary" class="continue-btn" @click="handleVerify">
            Verify
          </el-button>

          <!-- Button for Step 2: Submit registration -->
          <el-button v-if="registerStep === 2" type="primary" class="continue-btn" :loading="isSubmitting" @click="handleSubmit">
            Submit
          </el-button>
          <p v-if="registerStep === 2" class="terms-text">
            By clicking the "Submit" button, you are creating an account, and you agree to the Terms of Use
          </p>
        </div>
      </div>

      <div class="dialog-footer">
        <div class="divider" />
        <p class="login-hint">
          Already have an account?
          <el-button type="text" class="login-btn" @click="switchToLogin">
            Log in
          </el-button>
        </p>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
:deep(.signup-dialog) {
  border-radius: 40px;
  padding: 0;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 0;
}

:deep(.el-dialog__body) {
  padding: 0 24px;
}

.dialog-content {
  min-height: 400px;
}

.dialog-header {
  display: flex;
  font-size: 24px;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
}

.close-icon {
  cursor: pointer;
  font-size: 18px;
}

.welcome-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;
  word-break: break-all;
}

.section-title {
  font-weight: 500;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.verified {
  /* color: #67c23a; */
}

.verified-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #ffff;
  margin-right: 8px;
  background-color: #67c23a;
  border-radius: 50%;
  padding: 2px;
}

.email-input,
.form-input {
  width: 100%;
}

.field-label {
  margin-bottom: 8px;
  font-size: 14px;
}

.terms-text {
  font-size: 12px;
  color: #606266;
  margin-top: 20px;
  line-height: 1.5;
}

.continue-btn {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  background-color: #283b5c;
  border: none;
  margin-top: 20px;
}

.dialog-footer {
  padding: 0 20px 20px 20px;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 20px 0;
}

.login-hint {
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.login-btn {
  color: #409eff;
  padding: 0;
}

.password-strength-meter {
  padding: 10px;
}

.strength-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
  word-break: break-all;
}

.icon-warning {
  color: #ff994f;
  margin-right: 8px;
  border-radius: 50%;
  font-size: 16px;
}

.icon-success {
  color: #ffff;
  margin-right: 8px;
  background-color: #67c23a;
  border-radius: 50%;
  font-size: 14px;
  padding: 2px;
}

:deep(.password-strength-popover) {
  /* padding: 12px; */
  /* border: 1px solid #ebeef5; */
  /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
}
</style>
