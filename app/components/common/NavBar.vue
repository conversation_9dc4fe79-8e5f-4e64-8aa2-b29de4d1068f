<script setup lang="ts">
import { ArrowDown, UserFilled } from '@element-plus/icons-vue'
// 引入 Element Plus 的消息提示，用于反馈
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'
import { useUserStore } from '~/composables/user'
import ForgetPass from './ForgetPass.vue'
import Login from './Login.vue'
import Logo from './Logo.vue'
import Register from './Register.vue'

// 引入用户Store
const userStore = useUserStore()
// 使用计算属性监听登录状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 引用Login组件，使用适当的类型
const loginRef = ref<InstanceType<typeof Login> | null>(null)

function openUs() {
  window.open('http://www.esicint.com/about-us')
}
// 显示登录弹窗
function showLogin() {
  loginRef.value?.showLoginDialog()
}

// 引用Register组件，使用适当的类型
const registerRef = ref<InstanceType<typeof Register> | null>(null)
// 显示注册弹窗
function showRegister() {
  registerRef.value?.showRegisterDialog()
}

// 退出登录
async function logout() {
  try {
    // 调用后端 API 执行登出
    await $fetch('/api/auth/logout', { method: 'POST' })

    // 后端成功处理后，清除前端用户状态
    userStore.clearUserInfo()

    // 可选：给用户成功提示
    ElMessage.success('You have been successfully logged out.')

    // 可以在这里添加页面刷新或重定向逻辑，如果需要的话
    navigateTo('/')
  }
  catch (error) {
    console.error('Logout failed:', error)
    // 可选：给用户失败提示
    ElMessage.error('Logout failed.')
    // 即使API调用失败，也尝试清除本地状态，以防万一
    userStore.clearUserInfo()
  }
}
</script>

<template>
  <div class="p-4 px-8 flex h-71px justify-between">
    <div>
      <Logo />
    </div>
    <div>
      <div class="flex items-center flex-justify-end">
        <el-menu
          popper-class="el-menu-popper-demo"
          :ellipsis="false"
          mode="horizontal"
          :popper-offset="16"
          style="max-width: 600px"
        >
          <el-sub-menu index="2" disabled>
            <template #title>
              Strategy
            </template>
            <el-menu-item index="2-1">
              item one
            </el-menu-item>
            <el-menu-item index="2-2">
              item two
            </el-menu-item>
            <el-menu-item index="2-3">
              item three
            </el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="3" disabled>
            <template #title>
              Service
            </template>
            <el-menu-item index="3-1">
              item one
            </el-menu-item>
            <el-menu-item index="3-2">
              item two
            </el-menu-item>
            <el-menu-item index="3-3">
              item three
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
        <el-button
          link type="primary" class="mx-4 !font-size-16px !color-#303133 !hover:color-#28738E"
          @click="openUs"
        >
          About Us
        </el-button>

        <!-- 未登录状态显示登录和注册按钮 -->
        <template v-if="!isLoggedIn && !userStore.userInfo?.nickname">
          <el-button @click="showLogin">
            Log in
          </el-button>
          <el-button type="primary" @click="showRegister">
            Register
          </el-button>
        </template>

        <!-- 登录状态显示用户信息和菜单 -->
        <template v-else>
          <el-dropdown trigger="click">
            <div class="user-profile-container">
              <el-avatar
                :size="32"
                class="mr-2 !bg-white"
              >
                <el-icon color="#314564" :size="20">
                  <UserFilled />
                </el-icon>
              </el-avatar>
              <span class="user-name">{{ userStore.userInfo?.nickname || 'User' }}</span>
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="navigateTo('/collection')">
                  Saved suppliers
                </el-dropdown-item>
                <el-dropdown-item disabled>Price info</el-dropdown-item>
                <el-dropdown-item @click="navigateTo('/account')">
                  Account setting
                </el-dropdown-item>
                <el-dropdown-item divided @click="logout">
                  Log out
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </div>
      <Login ref="loginRef" />
      <Register ref="registerRef" />
      <ForgetPass />
    </div>
  </div>
</template>

<style lang="scss">
.el-menu-popper-demo {
  border-radius: 12px !important;

  ul {
    padding: 5px !important;
    border-radius: 12px !important;
  }

  .el-menu-item {
    border-radius: 8px;
    margin: 4px;
    height: 40px;
    line-height: 40px;

    &:hover {
      background-color: #f8f8f8;
      color: #28738e;
      border: none;
    }
  }
}

/* 组件内部样式 */
</style>

<style lang="scss" scoped>
:deep(.el-sub-menu__title) {
  border-bottom: none !important;
}
:deep(.el-menu--horizontal) {
  --el-menu-horizontal-height: 32px;
  background: transparent;
  border: none !important;
}
:deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover) {
  color: #28738e;
  background: transparent;
}
:deep(.el-menu--horizontal > .el-sub-menu .is-active .el-sub-menu__title) {
  border: none !important;
}

:deep(.el-menu--popup .el-menu-item:hover) {
  background-color: #f8f8f8;
  color: #28738e;
}
:deep(.el-popper) {
  border-radius: 12px !important;
}

/* 用户信息样式 */
.user-profile-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  height: 40px;
  border-radius: 20px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f8f8f8;
  }

  .user-name {
    margin: 0 8px;
    font-size: 16px;
    color: #303133;
  }
}
</style>
