<script setup lang="ts">
import type { CheckboxValueType, FormInstance, FormRules } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { useUserStore } from '~/composables/user'
import emitter from '~/utils/emitter'

interface LoginResponse {
  code: number
  data: {
    userId: number
    accessToken: string
    refreshToken: string
    expiresTime: number
  }
  msg: string
}

interface PermissionResponse {
  code: number
  data: {
    user: {
      id: number
      nickname: string
      avatar: string
      deptId: number
    }
    roles: string[]
    permissions: string[]
  }
  msg: string
}

const dialogVisible = ref(false)
const form = ref({
  mail: '',
  password: '',
  tenantName: '芋道源码',
  rememberMe: true,
})

// 保存初始状态以便重置
const initialForm = {
  mail: '',
  password: '',
  tenantName: '',
  rememberMe: true,
}

const formRef = ref<FormInstance>()
const loading = ref(false)

const rules = reactive<FormRules>({
  mail: [
    { required: true, message: 'Enter your email', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email', trigger: 'blur' },
  ],
  password: [
    { required: true, message: 'Incorrect ID or password. Please re-enter.', trigger: 'blur' },
  ],
})

const userStore = useUserStore()

// 重置表单到初始状态
function resetForm() {
  // 如果有存储的凭据且记住我已勾选，使用存储的凭据
  const savedCredentials = localStorage.getItem('userCredentials')
  if (savedCredentials) {
    try {
      const credentials = JSON.parse(savedCredentials)
      form.value = {
        mail: credentials.mail || '',
        password: credentials.password || '',
        tenantName: '',
        rememberMe: true,
      }
    }
    catch (error) {
      console.error('加载保存的登录信息失败:', error)
      localStorage.removeItem('userCredentials')
      form.value = { ...initialForm }
    }
  }
  else {
    form.value = { ...initialForm }
  }

  loading.value = false

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 从本地存储加载保存的登录信息
onMounted(() => {
  resetForm()

  // 监听打开登录对话框事件
  emitter.on('open-login', () => {
    resetForm()
    dialogVisible.value = true
  })
})

// 监听对话框关闭，重置表单
watch(() => dialogVisible.value, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  emitter.off('open-login')
})

async function handleLogin() {
  if (!formRef.value)
    return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        const loginRes = await $fetch<LoginResponse>('/api/auth/login', {
          method: 'POST',
          body: {
            tenantName: form.value.tenantName,
            mail: form.value.mail,
            password: form.value.password,
            rememberMe: form.value.rememberMe,
          },
        })

        if (loginRes.code !== 0) {
          ElMessage.error(loginRes.msg || '登录失败')
          return
        }

        // 如果勾选了记住我，则保存登录信息到本地存储
        if (form.value.rememberMe) {
          localStorage.setItem('userCredentials', JSON.stringify({
            mail: form.value.mail,
            password: form.value.password,
          }))
        }
        else {
          // 如果未勾选，则清除之前保存的登录信息
          localStorage.removeItem('userCredentials')
        }

        userStore.setToken(loginRes.data.accessToken)

        const permissionRes = await $fetch<PermissionResponse>('/api/auth/get-permission-info')

        if (permissionRes.code === 0) {
          userStore.setPermissionInfo(permissionRes.data)

          dialogVisible.value = false
          ElMessage.success(`Welcome，${permissionRes.data.user.nickname}`)
          emitter.emit('login-success')
        }
        else {
          ElMessage.error(permissionRes.msg || '获取用户信息失败')
        }
      }
      catch (error) {
        console.error('登录出错:', error)
        ElMessage.error('登录请求失败，请稍后重试')
      }
      finally {
        loading.value = false
      }
    }
  })
}

// 处理记住我复选框变化
function handleRememberChange(val: CheckboxValueType) {
  if (!val) {
    localStorage.removeItem('userCredentials')
  }
}

function showLoginDialog() {
  resetForm()
  dialogVisible.value = true
}

// 切换到注册页面
function switchToRegister() {
  dialogVisible.value = false
  emitter.emit('open-register')
}

// 切换到忘记密码页面
function switchToForgetPass() {
  dialogVisible.value = false
  emitter.emit('open-forget-pass', 'login')
}

defineExpose({
  showLoginDialog,
})
</script>

<template>
  <div class="login-container">
    <el-dialog
      v-model="dialogVisible"
      :z-index="1001"
      width="440px"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      custom-class="login-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <div>Log in to ESiC Bole</div>
          <el-icon class="close-icon" @click="dialogVisible = false">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="dialog-content">
        <p class="welcome-text">
          Welcome. Enter your credentials to access ESiC Bole
        </p>

        <el-form ref="formRef" :hide-required-asterisk="true" :model="form" :rules="rules" label-position="top">
          <el-form-item label="Email" prop="mail">
            <el-input
              v-model="form.mail"
              placeholder="Input mail"
              class="form-input"
            />
          </el-form-item>

          <el-form-item label="Password" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="Input password"
              class="form-input"
              show-password
            />
          </el-form-item>

          <el-button type="primary" class="login-btn" :loading="loading" @click="handleLogin">
            Log in
          </el-button>
        </el-form>
      </div>

      <div class="dialog-footer">
        <div class="remember-forgot">
          <el-checkbox v-model="form.rememberMe" @change="handleRememberChange">
            Remember me
          </el-checkbox>
          <el-button type="text" class="forgot-btn" @click="switchToForgetPass">
            Forgot password?
          </el-button>
        </div>

        <div class="divider" />
        <p class="signup-hint">
          Don't have an account?
          <el-button type="text" class="signup-btn" @click="switchToRegister">
            Sign up
          </el-button>
        </p>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.login-dialog) {
  border-radius: 16px;
  padding: 0;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 0;
}

:deep(.el-dialog__body) {
  padding: 0 24px 24px;
}

.dialog-header {
  display: flex;
  font-size: 24px;
  font-weight: 500;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
}

.welcome-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 24px;
}

.form-input {
  border-radius: 8px;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-btn {
  font-size: 14px;
  color: #409eff;
  padding: 0;
}

.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 230px;
}

.dialog-footer {
  margin-top: 16px;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin-bottom: 16px;
}

.signup-hint {
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.signup-btn {
  font-size: 14px;
  color: #409eff;
  padding: 0;
}
</style>
