<script setup lang="ts">
// 定义 props 和事件
const props = defineProps<{
  modelValue?: string
}>()

const emit = defineEmits<{
  (e: 'search', value: any): void
  (e: 'update:modelValue', value: string): void
}>()

// 使用 computed 实现双向绑定
const searchText = computed({
  get: () => props.modelValue || '',
  set: (value: string) => emit('update:modelValue', value),
})
const showServiceDetails = ref(false)
const { t } = useI18n()
const router = useRouter()
const localePath = useLocalePath()
let hideTimer = null

// 搜索函数
function doSearch() {
  emit('search', searchText.value)
}

// 跳转到联系页面
function goToContactPage() {
  router.push(localePath('contact'))
}

// 隐藏服务详情
function hideServiceDetails() {
  hideTimer = setTimeout(() => {
    showServiceDetails.value = false
  }, 1000)
}

// 取消隐藏
function cancelHideServiceDetails() {
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}
</script>

<template>
  <el-affix class="!w-full">
    <div style="background: #DAE8F0">
      <div class="flex w-full">
        <div class="homeInput px-8 py-4 flex w-full justify-between">
          <div class="flex flex-[60%_0_1]">
            <el-input
              v-model="searchText"
              class="b-rd-8px b-r-none h-50px"
              placeholder="Search by category, supplier, or keyword"
              @keyup.enter="doSearch"
            >
              <template #append>
                <el-button
                  text
                  class="text-5 border-none bg-transparent !font-bold"
                  style="height: 50px;border-radius: 0 8px 8px 0;"
                  @click="doSearch"
                >
                  Search
                </el-button>
              </template>
            </el-input>
            <div class="service-card-container flex-none">
              <div
                class="service-card color-#314564 ml-2 p-1 px-4 b-rd-2 flex cursor-pointer items-center"
                style="background: #E5EFF5"
                @mouseenter="showServiceDetails = true; cancelHideServiceDetails()"
                @mouseleave="hideServiceDetails()"
              >
                <Icon name="fluent:premium-24-regular" size="24" />
                <div class="font-size-14px ml-4">
                  <span>Contact an ESiC Professional</span>
                  <br>
                  <span>Procurement Outsourcing Services</span>
                </div>

                <!-- 悬停时显示的服务详情卡片 -->
                <div
                  v-if="showServiceDetails"
                  class="service-details z-10999"
                  @mouseenter="cancelHideServiceDetails()"
                  @mouseleave="hideServiceDetails()"
                >
                  <div class="service-list">
                    <div class="service-item">
                      <span class="service-bullet" />
                      <span>Supplier Onboarding</span>
                    </div>
                    <div class="service-item">
                      <span class="service-bullet" />
                      <span>Inquiry & Biz Negotiation</span>
                    </div>
                    <div class="service-item">
                      <span class="service-bullet" />
                      <span>NPI Support</span>
                    </div>
                    <div class="service-item">
                      <span class="service-bullet" />
                      <span>Quality & Coaching</span>
                    </div>
                  </div>
                  <div class="contact-button" @click="router.push(localePath('contact'))">
                    Contact ESiC
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="text-base text-gray-700 b-rd-full flex items-center justify-center">
            <Categories />
            <div class="text-gray px-6">
              |
            </div>
            <VettedSupplier />
            <div class="text-gray px-6">
              |
            </div>
            <Buyers />
          </div>
        </div>
      </div>
    </div>
  </el-affix>
</template>

<style scoped>
:deep(.el-input-group__append) {
  font-size: 20px;
  font-weight: bold;
  background-color: #ffffff;
  color: #2c3e50;
  border-radius: 0 8px 8px 0;
  border-left: none;
}

.homeInput {
  :deep(.el-input__wrapper) {
    border-radius: 8px 0 0 8px;
    padding-right: 0;
    background: #fff;
  }
}

/* 服务卡片容器 */
.service-card-container {
  position: relative;
}

/* 服务卡片基本样式 */
.service-card {
  position: relative;
  transition: all 0.3s ease;
}

/* 服务详情卡片 */
.service-details {
  position: absolute;
  top: 115%;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  padding: 16px;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 服务列表 */
.service-list {
  margin-bottom: 16px;
}

/* 服务项目 */
.service-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #314564;
}

/* 服务项目前的圆点 */
.service-bullet {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4996b8;
  margin-right: 12px;
}

/* 联系按钮 */
.contact-button {
  background: #ffffff;
  border: 1px solid #314564;
  border-radius: 8px;
  color: #314564;
  padding: 10px;
  text-align: center;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.contact-button:hover {
  background-color: #f5f7fa;
}

.stats-item {
  position: relative;
  overflow: hidden;
}
</style>
