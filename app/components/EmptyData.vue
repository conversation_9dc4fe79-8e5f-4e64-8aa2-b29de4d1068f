<template>
  <div :class="containerClass">
    <slot>
      {{ text }}
    </slot>
  </div>
</template>

<script setup lang="ts">
interface Props {
  /**
   * 显示的文本内容
   */
  text?: string
  /**
   * 显示类型：
   * - 'inline': 内联显示，用于表格字段等
   * - 'block': 块级显示，用于较大的空数据区域
   * - 'custom': 自定义样式
   */
  type?: 'inline' | 'block' | 'custom'
  /**
   * 自定义CSS类名
   */
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: 'No data',
  type: 'inline',
  customClass: ''
})

const containerClass = computed(() => {
  if (props.type === 'custom') {
    return props.customClass
  }
  
  const baseClasses = {
    inline: 'text-gray-500',
    block: 'text-sm text-gray-400'
  }
  
  return baseClasses[props.type] || baseClasses.inline
})
</script>
