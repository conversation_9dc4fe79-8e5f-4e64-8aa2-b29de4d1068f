<script setup lang="ts">
import type { EChartsCoreOption } from 'echarts'
import { nextTick, onMounted, ref } from 'vue'
import { useCategory } from '~/composables'
import { useCompanyHomepage } from '~/composables/api/modules/company'
import EChartComponent from '../common/EChartComponent.vue'

const { getCategoryNameById } = useCategory()
const { getDictLabel } = useCacheStore()

const loading = ref(false)
const hasData = ref(false)
const popoverVisible = ref(false)
const totalVettedSuppliers = ref(0)

// 图表组件引用
const supplierChartRef = ref<InstanceType<typeof EChartComponent>>()
const categoryChartRef = ref<InstanceType<typeof EChartComponent>>()
const industryChartRef = ref<InstanceType<typeof EChartComponent>>()
const regionalChartRef = ref<InstanceType<typeof EChartComponent>>()

// 处理 popover 显示后的图表 resize
async function handlePopoverShow() {
  popoverVisible.value = true
  await nextTick()

  // 延迟一点时间确保 popover 完全显示
  setTimeout(() => {
    supplierChartRef.value?.resize()
    categoryChartRef.value?.resize()
    industryChartRef.value?.resize()
    regionalChartRef.value?.resize()
  }, 150)
}

function handlePopoverHide() {
  popoverVisible.value = false
}

onMounted(async () => {
  loading.value = true
  try {
    const response = await useCompanyHomepage()
    if (response.code === 0 && response.data) {
      const { searchTypeList } = response.data

      // 计算总的 Vetted Suppliers 数量
      if (searchTypeList.supplierType && searchTypeList.supplierType.length > 0) {
        totalVettedSuppliers.value = searchTypeList.supplierType.at(-1).count
      }

      // 更新供应商细分图表数据
      if (searchTypeList.supplierType && searchTypeList.supplierType.length > 0) {
        supplierSegmentationOption.value.yAxis.data = searchTypeList.supplierType.map(item => item.name)
        supplierSegmentationOption.value.series[0].data = searchTypeList.supplierType.map((item) => {
          // 为每个数据项添加单独的标签配置
          return {
            value: item.count,
            // 如果数值较大，可以调整标签位置
            label: {
              show: true,
              position: 'right',
              formatter: '{c}',
              color: '#666',
              distance: 5,
              fontSize: 12,
              overflow: 'truncate',
            },
          }
        })
        hasData.value = true
      }

      // 更新类别细分图表数据
      if (searchTypeList.category && searchTypeList.category.length > 0) {
        const categoryData = searchTypeList.category.map(item => ({
          value: item.count,
          name: item.name,
        }))

        categoryBreakdownOption.value.legend.formatter = (name: string) => {
          const item = categoryData.find(d => d.name === name)
          return `${name} ${item?.value || ''}`
        }

        categoryBreakdownOption.value.series[0].data = categoryData.map((item, index) => {
          const colors = ['#004D61', '#6DD3CE', '#004D61', '#E8F1F2', '#E8F1F2', '#6DD3CE']
          return {
            ...item,
            itemStyle: { color: colors[index % colors.length] },
          }
        })
        hasData.value = true
      }

      // 更新行业细分图表数据
      if (searchTypeList.industry && searchTypeList.industry.length > 0) {
        const industryData = searchTypeList.industry.slice(0, 5).map(item => ({
          value: item.count,
          name: item.name,
        }))

        industryBreakdownOption.value.legend.formatter = (name: string) => {
          const item = industryData.find(d => d.name === name)
          return `${name} ${item?.value || ''}`
        }

        industryBreakdownOption.value.series[0].data = industryData.map((item, index) => {
          const colors = ['#004D61', '#6DD3CE', '#004D61', '#E8F1F2', '#004D61', '#E8F1F2', '#6DD3CE']
          return {
            ...item,
            itemStyle: { color: colors[index % colors.length] },
          }
        })
        hasData.value = true
      }

      // 更新区域细分图表数据
      if (searchTypeList.region && searchTypeList.region.length > 0) {
        regionalBreakdownOption.value.xAxis.data = searchTypeList.region.map(item => item.name)
        regionalBreakdownOption.value.series[0].data = searchTypeList.region.map((item) => {
          // 为每个数据项添加单独的标签配置
          return {
            value: item.count,
            // 如果数值较大，可以调整标签位置
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              color: '#666',
              distance: 5,
              fontSize: 12,
              overflow: 'truncate',
            },
          }
        })
        hasData.value = true
      }
    }
  }
  catch (error) {
    console.error('获取首页公司数据失败:', error)
  }
  finally {
    loading.value = false
  }
})

// 供应商细分图表配置
const supplierSegmentationOption = ref<EChartsCoreOption>({
  grid: {
    left: '3%',
    right: '15%',
    bottom: '3%',
    top: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    show: false,
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { show: false },
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: '#666',
    },
    axisLine: { show: false },
    axisTick: { show: false },
  },
  series: [
    {
      name: '数量',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#6DD3CE',
        borderRadius: [0, 6, 6, 0], // 右侧圆角
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{c}',
        color: '#666',
        distance: 5,
        fontSize: 12,
        overflow: 'truncate',
      },
      barWidth: '40%',
    },
  ],
})

// 类别细分图表配置
const categoryBreakdownOption = ref<EChartsCoreOption>({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    orient: 'horizontal',
    right: '10%',
    top: '15%',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 12,
    width: 220,
    height: 80,
    textStyle: {
      color: '#666',
      fontSize: 13,
    },
    formatter: (name: string) => {
      return name
    },
  },
  series: [
    {
      // name: '类别细分',
      type: 'pie',
      radius: ['30%', '75%'],
      center: ['25%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 0,
        borderColor: '#fff',
        borderWidth: 1,
      },
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
})

// 行业细分图表配置
const industryBreakdownOption = ref<EChartsCoreOption>({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    orient: 'horizontal',
    right: '10%',
    top: '15%',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 12,
    width: 220,
    height: 80,
    textStyle: {
      color: '#666',
      fontSize: 13,
    },
    formatter: (name: string) => {
      return name
    },
  },
  series: [
    {
      // name: '行业细分',
      type: 'pie',
      radius: ['30%', '75%'],
      center: ['25%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 0,
        borderColor: '#fff',
        borderWidth: 1,
      },
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      data: [],
    },
  ],
})

// 区域细分图表配置
const regionalBreakdownOption = ref<EChartsCoreOption>({
  grid: {
    left: '3%',
    right: '4%',
    bottom: '5%',
    top: '15%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: '#666',
      interval: 0,
      margin: 8,
    },
    axisLine: { show: false },
    axisTick: { show: false },
  },
  yAxis: {
    type: 'value',
    show: false,
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { show: false },
  },
  series: [
    {
      name: '数量',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#6DD3CE',
        borderRadius: [6, 6, 0, 0], // 顶部圆角
      },
      label: {
        show: true,
        position: 'top',
        formatter: '{c}',
        color: '#666',
        distance: 5,
        fontSize: 12,
        overflow: 'truncate',
      },
      barWidth: '40%',
    },
  ],
})
</script>

<template>
  <el-popover
    class="box-item"
    placement="bottom-start"
    :width="1000"
    trigger="hover"
    popper-class="supplier-popover"
    @show="handlePopoverShow"
    @hide="handlePopoverHide"
  >
    <template #reference>
      <div class="stats-item p-4 pl-7 b-rd-full cursor-pointer truncate transition-all duration-300 hover:text-#28738E hover:bg-white">
        {{ totalVettedSuppliers.toLocaleString() }}+ Vetted Suppliers
      </div>
    </template>

    <div class="supplier-charts-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="hasData" class="charts-grid">
        <div class="chart-box top-left">
          <div class="chart-title">
            Supplier Segmentation
          </div>
          <EChartComponent
            ref="supplierChartRef"
            :option="supplierSegmentationOption"
            height="156px"
            width="100%"
          />
        </div>
        <div class="chart-box top-right">
          <div class="chart-title">
            Category Breakdown
          </div>
          <EChartComponent
            ref="categoryChartRef"
            :option="categoryBreakdownOption"
            height="156px"
            width="100%"
          />
        </div>
        <div class="chart-box bottom-left">
          <div class="chart-title">
            Industry Breakdown
          </div>
          <EChartComponent
            ref="industryChartRef"
            :option="industryBreakdownOption"
            height="156px"
            width="100%"
          />
        </div>
        <div class="chart-box bottom-right">
          <div class="chart-title">
            Regional Breakdown
          </div>
          <EChartComponent
            ref="regionalChartRef"
            :option="regionalBreakdownOption"
            height="156px"
            width="100%"
          />
        </div>
      </div>
      <div v-else class="empty-state">
        <el-empty description="暂无数据" />
      </div>
    </div>
  </el-popover>
</template>

<style scoped>
.supplier-charts-container {
  background-color: white;
  border-radius: 8px;
  width: 100%;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 0;
  width: 100%;
  height: 414px;
  position: relative;
}

/* 十字分割线 */
.charts-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 1px;
  height: 100%;
  background-color: #e5e7eb;
  z-index: 10;
  transform: translateX(-50%);
}

.charts-grid::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #e5e7eb;
  z-index: 10;
  transform: translateY(-50%);
}

.chart-box {
  background-color: #fff;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-width: 300px;
  position: relative;
  padding: 15px;
}

.chart-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 10px;
}

.loading-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 414px;
}

:deep(.supplier-popover) {
  padding: 0;
}
</style>

<style>
.supplier-popover {
  border-radius: 12px !important;
}
</style>
