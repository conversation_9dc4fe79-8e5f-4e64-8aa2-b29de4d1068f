<script setup lang="ts">
import { useCacheStore } from '~/composables'

const { t } = useI18n()
const cacheStore = useCacheStore()
// 添加品类数据（有层级结构）
const categories = ref([])
const router = useRouter()
const route = useRoute()
const localePath = useLocalePath()

function addCategoryFilter(category) {
  // 判断当前路由是否为首页
  if (route.path === '/') {
    router.push({
      path: localePath('search'),
      query: {
        category: category.name,
      },
    })
  }
  else {
    emitter.emit('add-search-category', category.name)
  }
}

onMounted(() => {
  categories.value = cacheStore.getCategoryTree
})

// 计算品类总数量（包括一级和二级品类），取整到十位数
const totalCategoriesCount = computed(() => {
  let count = 0
  categories.value.forEach((category) => {
    count++ // 一级品类
    if (category.children && category.children.length > 0) {
      count += category.children.length // 二级品类
    }
  })
  // 向上取整到十位数（例如：47 -> 50, 123 -> 130）
  return Math.ceil(count / 10) * 10
})

// 当前悬停的一级品类
const hoverCategoryIndex = ref(-1)
function showPopover() {
  if (hoverCategoryIndex.value === -1) {
    setHoverCategory(0)
  }
}
// 设置当前悬停的品类
function setHoverCategory(index: number) {
  hoverCategoryIndex.value = index
}

// 计算二级品类项目的动态宽度
const secondaryCategoryItemWidth = computed(() => {
  if (hoverCategoryIndex.value >= 0 && hoverCategoryIndex.value < categories.value.length) {
    const currentCategory = categories.value[hoverCategoryIndex.value]
    if (currentCategory.children && currentCategory.children.length > 0) {
      const count = currentCategory.children.length
      // 根据数量动态调整宽度
      if (count > 16) {
        return 'calc(25% - 6px)' // 8个或以上时使用25%（4列）
      }
      else {
        return 'calc(50% - 6px)' // 少于4个时使用100%（1列）
      }
    }
  }
  return 'calc(25% - 6px)' // 默认值
})
</script>

<template>
  <el-popover
    class="box-item"
    placement="bottom-start"
    :width="820"
    trigger="hover"
    popper-class="categories-popover"
    @show="showPopover"
  >
    <template #reference>
      <div class="stats-item p-4 pl-7 b-rd-full cursor-pointer truncate transition-all duration-300 hover:text-#28738E hover:bg-white">
        {{ t('home.stats.categories', { count: totalCategoriesCount }) }}
      </div>
    </template>
    <template #default>
      <div class="categories-container">
        <div class="categories-layout">
          <!-- 左侧一级品类列表 -->
          <div class="primary-categories">
            <div
              v-for="(category, index) in categories"
              :key="`primary-${index}`"
              class="primary-category-item"
              :class="{ active: hoverCategoryIndex === index }"
              @mouseenter="setHoverCategory(index)"
            >
              <span style="flex: 1;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{{ category.name }}</span>
              <span v-if="category.children && category.children.length > 0" class="arrow-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </span>
            </div>
          </div>

          <!-- 右侧二级品类列表 -->
          <div class="secondary-categories">
            <div v-if="hoverCategoryIndex >= 0 && hoverCategoryIndex < categories.length && categories[hoverCategoryIndex].children && categories[hoverCategoryIndex].children.length > 0" class="secondary-categories-grid">
              <div
                v-for="(subCategory, subIndex) in categories[hoverCategoryIndex].children"
                :key="`secondary-${subIndex}`"
                class="secondary-category-item"
                :style="{ width: secondaryCategoryItemWidth }"
                @click="addCategoryFilter(subCategory)"
              >
                {{ subCategory.name }}
              </div>
            </div>
            <!--            <div v-else class="no-subcategory-message"> -->
            <!--              请将鼠标悬停在左侧品类上查看详细分类 -->
            <!--            </div> -->
          </div>
        </div>
      </div>
    </template>
  </el-popover>
</template>

<style scoped>
.stats-item {
  position: relative;
  overflow: hidden;
}

/* 品类弹出框样式 */
:deep(.el-popper) {
  padding: 16px;
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.categories-container {
  width: 100%;
}

.categories-layout {
  display: flex;
  width: 100%;
  min-height: 280px;
}

.primary-categories {
  width: 30%;
  border-right: 1px solid #e8e8e8;
  padding-right: 12px;
}

.primary-category-item {
  padding: 8px 10px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.primary-category-item:hover,
.primary-category-item.active {
  font-weight: bold;
  background-color: #f0f9ff;
  color: #28738e;
}

.arrow-icon {
  display: flex;
  align-items: center;
}

.secondary-categories {
  width: 70%;
  padding-left: 16px;
  display: flex;
  align-items: flex-start;
}

.secondary-categories-grid {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  align-content: flex-start;
  overflow-x: hidden;
}

.secondary-category-item {
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
}

.secondary-category-item:hover {
  font-weight: bold;
  background-color: #f0f9ff;
  color: #28738e;
}

.no-subcategory-message {
  color: #999;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 40px 0;
}
</style>

<style>
.categories-popover {
  border-radius: 12px !important;
}
</style>
