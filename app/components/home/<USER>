<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'
// BuyersPopover组件
import { useCompanyBuyer } from '~/composables/api/modules/company'

defineOptions({
  name: 'BuyersPopover',
})

const { t } = useI18n()

// 定义买家数据类型
interface Buyer {
  id: number
  buyerName: string
  remark?: string
  avatar?: string
}

// 接口数据
const loading = ref(false)
const buyerList = ref<Buyer[]>([])
const pageSize = ref(8)
const pageNum = ref(1)

// 获取买家数据
async function fetchBuyers() {
  loading.value = true
  try {
    const res = await useCompanyBuyer({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    })

    if (res.code === 0 && res.data) {
      buyerList.value = res.data
    }
  }
  catch (error) {
    console.error('获取买家数据失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 初始化加载数据
onMounted(() => {
  fetchBuyers()
})
</script>

<template>
  <el-popover
    placement="bottom-end"
    :width="950"
    trigger="hover"
    popper-class="buyers-popover"
  >
    <template #reference>
      <div class="stats-item p-4 pr-7 b-rd-full cursor-pointer truncate transition-all duration-300 hover:text-#28738E hover:bg-white">
        {{ t('home.stats.buyers', { count: 20 }) }}
      </div>
    </template>
    <div class="buyers-container p-4">
      <div v-if="loading" class="p-8 flex items-center justify-center">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="buyerList.length > 0" class="gap-4 grid grid-cols-3">
        <div v-for="buyer in buyerList.slice(0, 8)" :key="buyer.id" class="buyer-card p-4 flex items-start">
          <div v-if="buyer.avatar" class="mr-4 rounded-full flex-shrink-0 h-16 w-16 overflow-hidden">
            <img :src="buyer.avatar" class="h-full w-full object-cover" alt="buyer avatar">
          </div>
          <div v-else class="avatar-placeholder mr-4 rounded-full bg-gray-300 flex-shrink-0 h-16 w-16" />
          <div class="buyer-info">
            <h3 class="text-gray-700 font-bold mb-1">
              {{ buyer.buyerName }}
            </h3>
            <p v-if="buyer.remark" class="text-3 text-gray-600">
              {{ buyer.remark }}
            </p>
          </div>
        </div>
        <div class="mt-4 flex">
          <div class="text-#28738E cursor-pointer hover:underline" />
        </div>
      </div>
      <div v-else class="empty-state p-8 flex flex-col items-center justify-center">
        <el-icon class="text-5xl text-gray-400 mb-4">
          <Document />
        </el-icon>
        <p class="text-gray-500">
          暂无买家数据
        </p>
      </div>
    </div>
  </el-popover>
</template>

<style scoped>
.stats-item {
  position: relative;
  overflow: hidden;
}

:deep(.buyers-popover) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.buyers-container {
  background-color: white;
}

.buyer-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.buyer-card:hover {
  background-color: #f5f9fb;
}

.avatar-placeholder {
  min-width: 64px;
}

.empty-state {
  min-height: 200px;
}
</style>

<style>
.buyers-popover {
  border-radius: 12px !important;
}
</style>
