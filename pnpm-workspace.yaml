packages: []

catalogs:
  build:
    '@nuxt/devtools': ^2.3.2
    '@nuxt/eslint': ^1.3.0
    '@nuxtjs/color-mode': ^3.5.2
    '@pinia/nuxt': ^0.10.1
    '@unocss/eslint-config': ^66.1.0-beta.10
    '@unocss/nuxt': ^66.1.0-beta.10
    '@vite-pwa/nuxt': ^1.0.0
    '@vueuse/nuxt': ^13.1.0
    nuxt: ^3.16.2
    unocss: ^66.1.0-beta.10
    unplugin: ^2.2.2
    vite: ^6.2.5

  dev:
    '@antfu/eslint-config': ^4.11.0
    eslint: ^9.24.0
    eslint-plugin-format: ^1.0.1
    typescript: ^5.8.3
    vue-tsc: ^2.2.8

  frontend:
    '@vueuse/core': ^13.1.0
    pinia: ^3.0.1
    vue: ^3.5.13

  icons:
    '@iconify-json/carbon': ^1.2.8
    '@iconify-json/twemoji': ^1.2.2

onlyBuiltDependencies:
  - '@parcel/watcher'
  - esbuild
catalog:
  '@element-plus/icons-vue': ^2.3.1
  '@element-plus/nuxt': ^1.1.1
  '@iconify/vue': ^5.0.0
  '@nuxtjs/google-fonts': ^3.2.0
  '@nuxtjs/i18n': ^9.5.3
  '@sidebase/nuxt-auth': 0.10.1
  element-plus: ^2.9.6
  mitt: ^3.0.1
  next-auth: ~4.21.1
  nuxt-icon: 1.0.0-beta.7
  sass: ^1.86.0
  vuedraggable: ^4.1.0
