import { appDescription } from './app/constants'

export default defineNuxtConfig({
  modules: [
    '@element-plus/nuxt',
    '@vueuse/nuxt',
    '@unocss/nuxt',
    '@pinia/nuxt',
    '@nuxtjs/i18n',
    '@nuxt/eslint',
    '@nuxtjs/google-fonts',
    '@nuxt/icon',
  ],

  components: [
    {
      path: '~/components',
      pathPrefix: false,
    },
  ],
  devtools: {
    enabled: true,
  },

  app: {
    head: {
      viewport: 'width=device-width,initial-scale=1',
      link: [
        { rel: 'icon', href: '/favicon.ico', sizes: 'any' },
        // { rel: 'icon', type: 'image/svg+xml', href: '/nuxt.svg' },
        { rel: 'apple-touch-icon', href: '/apple-touch-icon.png' },
      ],
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: appDescription },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
        { name: 'theme-color', content: 'white' },
      ],
    },
  },
  css: [
    '@unocss/reset/tailwind.css',
    '~/assets/scss/index.scss',
  ],

  // 添加运行时配置，包含API基础URL
  runtimeConfig: {
    // 服务端可用的配置
    // Nuxt会自动从NUXT_ADMIN_API_BASE_URL环境变量读取，这里提供默认值
    adminApiBaseUrl: '', // 默认值，会被 NUXT_ADMIN_API_BASE_URL 环境变量覆盖

    // 同时在客户端和服务端可用的配置
    public: {
      // 如果需要在客户端访问，可以放在这里
    },
  },

  future: {
    compatibilityVersion: 4,
  },

  experimental: {
    // when using generate, payload js assets included in sw precache manifest
    // but missing on offline, disabling extraction it until fixed
    payloadExtraction: false,
    renderJsonPayloads: true,
    typedPages: true,
  },

  compatibilityDate: '2024-08-14',

  nitro: {
    esbuild: {
      options: {
        target: 'esnext',
      },
    },
    prerender: {
      crawlLinks: false,
      routes: ['/'],
      ignore: ['/hi'],
    },
  },

  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `@use "@/assets/scss/element/index.scss" as element;`,
        },
      },
    },
    optimizeDeps: {
      include: ['@popperjs/core', 'element-plus'],
    },
    ssr: {
      noExternal: ['@popperjs/core'],
    },
  },

  elementPlus: {
    icon: 'ElIcon',
    importStyle: 'scss',
    defaultLocale: 'en',
  },

  eslint: {
    config: {
      standalone: false,
      nuxt: {
        sortConfigKeys: true,
      },
    },
  },
  googleFonts: {
    families: {
      'Poppins': [400, 700],
      'Noto Sans': true,
    },
  },

  i18n: {
    locales: [
      {
        code: 'en-US',
        file: 'en.json',
      },
      {
        code: 'zh-CN',
        file: 'zh-CN.json',
      },
    ],
    langDir: 'locales',
    defaultLocale: 'en-US',
    detectBrowserLanguage: false, // TODO 暂时注释
  },

  // vueuse
  vueuse: {
    ssrHandlers: true,
  },
})
